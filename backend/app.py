from flask import Flask, request, jsonify, send_file, render_template
from flask_cors import CORS
from datetime import datetime, timedelta
from datadeal import get_last_weekday, get_same_weekday_last_year, get_gmv_change_result_single_date, \
    get_activity_attibutional_result, get_sheets_data
from datadeal import get_gmv_change_result_multi_date, get_attibutional_result, get_attributional_result_text
# from datadeal import get_supply_side_attribution_text, get_mixed_attribution_text  # AI归因结论只使用维度归因，已注释
from datadeal import get_activity_gmv_change_result_single_date, get_activity_gmv_change_result_multi_date
from datadeal import get_coupon_mechanisms_for_single_date_activity, get_coupon_mechanisms_for_date_range_activity
from datadeal import get_coupon_threshold_for_single_date_activity, get_coupon_threshold_for_date_range_activity
from datadeal import get_provinces_for_single_date_gmv, get_provinces_for_single_date_activity
from datadeal import get_coupon_discount_for_single_date_activity, get_coupon_discount_for_date_range_activity
from datadeal import get_provinces_for_date_range_gmv, get_provinces_for_date_range_activity
from datadeal import get_sub_brands_for_single_date_gmv, get_sub_brands_for_date_range_gmv
from datadeal import get_sub_brands_for_single_date_activity, get_sub_brands_for_date_range_activity
from datadeal import get_retailers_for_single_date_gmv, get_retailers_for_date_range_gmv
from datadeal import get_retailers_for_single_date_activity, get_retailers_for_date_range_activity
from datadeal import get_platform_for_single_date_gmv, get_platform_for_date_range_gmv
from datadeal import get_platform_for_single_date_activity, get_platform_for_date_range_activity
from datadeal import get_products_for_single_date_gmv, get_products_for_date_range_gmv
from datadeal import get_products_for_single_date_activity, get_products_for_date_range_activity
from datadeal import get_historical_gmv_changes, get_historical_activity_gmv_changes
from datadeal import get_cities_for_single_date_gmv, get_cities_for_single_date_activity, get_cities_for_date_range_gmv, get_cities_for_date_range_activity
from datadeal import get_cross_dimension_attribution_result
from datadeal import get_drill_down_gmv_result, get_drill_down_activity_result
from datadeal import get_marketing_activity_drill_down_data_for_excel
from datadeal import get_supply_side_attribution_result, get_supply_side_sheets_data
from datadeal import get_marketing_side_indicators_result, get_marketing_side_sheets_data
import pandas as pd
import os
import warnings

def ensure_json_serializable(obj):
    """
    确保对象可以被JSON序列化，递归处理嵌套的数据结构
    """
    if isinstance(obj, dict):
        return {k: ensure_json_serializable(v) for k, v in obj.items()
                if not isinstance(v, pd.DataFrame)}
    elif isinstance(obj, list):
        return [ensure_json_serializable(item) for item in obj
                if not isinstance(item, pd.DataFrame)]
    elif isinstance(obj, pd.DataFrame):
        print(f"警告: 发现DataFrame对象，已过滤")
        return None
    else:
        return obj
import copy
import json
import uuid
import itertools
warnings.filterwarnings('ignore')

app = Flask(__name__)
# 配置CORS，允许所有跨域请求
CORS(app, resources={r"/*": {"origins": "*"}}, supports_credentials=True)

# 定义生成的 Excel 文件路径
OUTPUT_FILE_PATH = "attribution_result.xlsx"

# 定义一个保存归因结果文件的目录
ATTRIBUTION_RESULT_DIR = "attribution_results"
os.makedirs(ATTRIBUTION_RESULT_DIR, exist_ok=True)

sheet_name_mapping = {
    "sub_brand": "子品牌",
    "province": "省份",
    "standard_city": "城市",
    "city": "城市",
    "vender_name": "零售商",
    "product_name": "商品",
    "platform": "平台",
    "upc": "UPC",
    "coupon_name": "券机制",
    "coupon_threshold": "券门槛",
    "coupon_discount": "优惠力度"
}

# 列名映射
column_name_mapping = {
    "sub_brand": "子品牌",
    "province": "省份",
    "standard_city": "城市",
    "city": "城市",
    "vender_name": "零售商",
    "product_name": "商品",
    "platform": "平台",
    "upc": "UPC",
    "coupon_name": "券机制",
    "coupon_threshold": "券门槛",
    "coupon_discount": "优惠力度",
    "gmv_target": "当期GMV",
    "activity_gmv_target": "当期活动GMV",
    "target_ratio": "当期GMV占比",
    "activity_target_ratio": "当期活动GMV占比",
    "base_ratio": "对比期GMV占比",
    "activity_base_ratio": "对比期活动GMV占比",
    "expense_target": "消耗当前值",
    "roi_target": "ROI当前值",
    "gmv_base": "对比期GMV",
    "activity_gmv_base": "对比期活动GMV",
    "expense_base": "消耗基准值",
    "roi_base": "ROI基准值",
    "gmv_change": "GMV变化值",
    "activity_gmv_change": "活动GMV变化值",
    "expense_change": "消耗变化值",
    "roi_change": "ROI变化值",
    "gmv_change_ratio": "GMV变化率",
    "activity_gmv_change_ratio": "活动GMV变化率",
    "expense_change_ratio": "消耗变化率",
    "roi_change_ratio": "ROI变化率",
    "contribution_to_total": "GMV贡献度",
    "gmv_contribution_to_total": "GMV贡献度",
    "activity_gmv_contribution_to_total": "活动GMV贡献度",
    "expense_contribution_to_total": "消耗贡献度"
}

def check_indicator_type(indicator_types, check_type):
    """
    检查指标类型是否包含指定类型
    
    Args:
        indicator_types: 指标类型字符串，可能包含'供给向'、'营销向'或'供给向,营销向'
        check_type: 要检查的类型，'供给向'或'营销向'
    
    Returns:
        bool: 是否包含指定类型
    """
    if not indicator_types:
        return False
    
    # 将字符串按逗号分割并去除空格
    types = [t.strip() for t in indicator_types.split(',')]
    return check_type in types

def has_supply_side_indicator(indicator_types):
    """检查是否包含供给向指标"""
    return check_indicator_type(indicator_types, '供给向')

def has_marketing_side_indicator(indicator_types):
    """检查是否包含营销向指标"""
    return check_indicator_type(indicator_types, '营销向')

# 中英文维度映射字典
dimension_mapping_gmv = {
    "平台": "platform",
    "省份": "province", 
    "城市": "standard_city",
    "零售商": "vender_name",
    "子品牌": "sub_brand",
    "商品名称": "product_name",
    "商品" : "product_name"
}
dimension_mapping_activity = {
    "平台": "platform",
    "省份": "province", 
    "城市": "city",
    "零售商": "vender_name",
    "子品牌": "sub_brand",
    "商品名称": "product_name",
    "券机制": "coupon_name",
    "券门槛": "coupon_threshold",
    "优惠力度": "coupon_discount"
}
def convert_analysis_dimensions(analysis_dimensions_str,attr_index):
    """
    将前端传入的中文维度字符串转换为英文维度列表
    
    Args:
        analysis_dimensions_str: 前端传入的中文维度字符串，如 "平台,省份,城市,零售商"
    
    Returns:
        list: 转换后的英文维度列表，如 ["platform", "province", "standard_city", "vender_name"]
    """
    if not analysis_dimensions_str or analysis_dimensions_str.strip() == "":
        return []
    
    # 分割字符串并去除空格
    chinese_dimensions = [dim.strip() for dim in analysis_dimensions_str.split(',') if dim.strip()]
    
    # 转换为英文维度
    english_dimensions = []
    for chinese_dim in chinese_dimensions:
        if attr_index == '全量GMV':
            english_dim = dimension_mapping_gmv.get(chinese_dim)
        else:
            english_dim = dimension_mapping_activity.get(chinese_dim)
        if english_dim:
            english_dimensions.append(english_dim)
        else:
            print(f"警告: 未找到维度 '{chinese_dim}' 的英文映射")
    
    return english_dimensions

def process_cross_dimension_combinations(dimension_combinations_str, attr_index):
    """
    处理前端传入的交叉维度组合数据
    
    Args:
        dimension_combinations_str: 前端传入的JSON字符串，包含维度组合信息
        attr_index: 归因指标类型
    
    Returns:
        list: 处理后的交叉维度组合列表
    """
    if not dimension_combinations_str:
        return []
    
    try:
        combinations = json.loads(dimension_combinations_str)
        processed_combinations = []
        
        for combo in combinations:
            if combo.get('dimensions') and len(combo['dimensions']) > 1:  # 至少需要2个维度才算交叉
                # 转换中文维度为英文维度
                english_dimensions = []
                for chinese_dim in combo['dimensions']:
                    if attr_index == '全量GMV':
                        english_dim = dimension_mapping_gmv.get(chinese_dim)
                    else:
                        english_dim = dimension_mapping_activity.get(chinese_dim)
                    if english_dim:
                        english_dimensions.append(english_dim)
                
                if len(english_dimensions) > 1:  # 确保转换后仍有多个维度
                    processed_combinations.append({
                        'id': combo.get('id'),
                        'dimensions': english_dimensions,
                        'chinese_dimensions': combo['dimensions']  # 保留中文维度用于显示
                    })
        
        return processed_combinations
    except (json.JSONDecodeError, KeyError) as e:
        print(f"处理交叉维度组合时出错: {e}")
        return []

def process_multi_select_params(request_args):
    """
    处理多选参数，将逗号分隔的字符串转换为适当的格式
    Args:
        request_args: Flask request.args对象
    Returns:
        dict: 处理后的参数字典
    """
    params = {}
    # 需要处理多选的参数列表
    multi_select_fields = [
        'sub_brand', 'province', 'city', 'retailer', 'platform', 'upc',
        'coupon_mechanism', 'coupon_threshold', 'coupon_discount'
    ]
    for field in multi_select_fields:
        value = request_args.get(field, '全部')
        # 如果包含逗号，说明是多选，保持原样传递给后端函数
        # build_multi_select_condition函数会处理多选逻辑
        params[field] = value

    # 特殊处理：同时支持 city 和 standard_city 参数
    # 如果前端传递的是 standard_city，也要映射到 city 字段
    standard_city_value = request_args.get('standard_city', '全部')
    if standard_city_value != '全部':
        params['city'] = standard_city_value

    # 处理其他单选参数
    params['brand'] = request_args.get('brand')
    params['attr_index'] = request_args.get('attr_index', '全量GMV')
    params['analysis_dimensions'] = request_args.get('analysis_dimensions', '')
    params['cross_dimension_enabled'] = request_args.get('cross_dimension_enabled', 'false').lower() == 'true'
    params['dimension_combinations'] = request_args.get('dimension_combinations', '[]')
    params['compare_detail_type'] = request_args.get('compare_detail_type')
    
    # 新增归因方法和指标类型参数
    params['attribution_methods'] = request_args.get('attribution_methods', '维度')
    params['indicator_types'] = request_args.get('indicator_types', '营销向')
    
    # 处理日期参数
    params['tar_date'] = request_args.get('tar_date')
    params['base_date'] = request_args.get('base_date')
    params['tar_start_date'] = request_args.get('tar_start_date')
    params['tar_end_date'] = request_args.get('tar_end_date')
    params['base_start_date'] = request_args.get('base_start_date')
    params['base_end_date'] = request_args.get('base_end_date')
    
    return params

def write_dict_to_excel(data_dict, output_file):
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        for sheet_name, df in data_dict.items():
            # 映射 sheet_name
            mapped_sheet_name = sheet_name_mapping.get(sheet_name, sheet_name)

            # 映射列名
            mapped_columns = [column_name_mapping.get(col, col) for col in df.columns]
            df_copy = df.copy()
            df_copy.columns = mapped_columns

            # 格式化数值字段，确保GMV相关字段都有千分位分隔符
            for col in df_copy.columns:
                if any(keyword in col for keyword in ['GMV', '当期值', '对比期值', '变化值', '核销金额', '消耗']):
                    # 对GMV相关的数值字段进行千分位格式化
                    if col not in ['变化率', '贡献度', '占比']:  # 排除百分比字段
                        df_copy[col] = df_copy[col].apply(lambda x:
                            f"{float(str(x).replace(',', '')):,.0f}"
                            if pd.notna(x) and str(x) != '-' and str(x).replace(',', '').replace('.', '').replace('-', '').isdigit()
                            else str(x)
                        )

            # 写入 Excel
            df_copy.to_excel(writer, sheet_name=mapped_sheet_name, index=False)

def optimize_sheets_data(sheets_data):
    """优化sheets_data的格式，减少tokens消耗"""
    try:
        # 如果是字符串，先尝试去除外层的引号
        if isinstance(sheets_data, str):
            # 去除可能存在的外层单引号
            if sheets_data.startswith("'") and sheets_data.endswith("'"):
                sheets_data = sheets_data[1:-1]
            
            # 尝试直接解析
            try:
                data_dict = json.loads(sheets_data)
            except Exception as e:
                print(f"第一次JSON解析失败: {str(e)}")
                
                # 特殊处理: 先提取所有键值对
                try:
                    import re
                    
                    # 将特殊格式转换为标准JSON结构
                    # 步骤1: 确保属性名有双引号
                    sheets_data = re.sub(r'(\w+):', r'"\1":', sheets_data)
                    
                    # 步骤2: 处理内部嵌套的JSON字符串，将内部的双引号替换为单引号
                    # 先处理内部的值，将"[{...}]"格式转换为可解析的JSON
                    result_dict = {}
                    pattern = r'"([^"]+)":"(\[.*?\])(?=,"|"\}|$)'
                    matches = re.findall(pattern, sheets_data)
                    
                    for key, value in matches:
                        # 修复内嵌JSON字符串的引号问题
                        # 将值中的\"转义为'，避免引号嵌套问题
                        fixed_value = value.replace('\\"', "'")
                        result_dict[key] = fixed_value
                    
                    # 如果没有匹配到任何键值对，则尝试解析原始字符串
                    if not result_dict:
                        print("无法提取键值对，尝试最后的解析方法")
                        # 直接尝试修复常见的JSON格式问题
                        sheets_data = sheets_data.replace('"{', '{').replace('}"', '}')
                        sheets_data = sheets_data.replace('["', '[').replace('"]', ']')
                        data_dict = json.loads(sheets_data)
                    else:
                        data_dict = result_dict
                except Exception as e2:
                    print(f"特殊处理失败: {str(e2)}")
                    
                    # 最后尝试: 使用正则表达式直接提取各部分数据
                    try:
                        result_dict = {}
                        
                        # 使用更简单的方法，直接按键分割字符串
                        parts = sheets_data.split('","')
                        for part in parts:
                            part = part.replace('{"', '').replace('"}', '')
                            if ':' in part:
                                key, value = part.split(':', 1)
                                # 移除引号
                                key = key.strip('"')
                                result_dict[key] = value
                        
                        if result_dict:
                            data_dict = result_dict
                        else:
                            print("所有解析方法都失败，返回原始数据")
                            return sheets_data
                    except Exception as e3:
                        print(f"最终解析尝试失败: {str(e3)}")
                        return sheets_data
        else:
            # 如果不是字符串，假设它已经是字典
            data_dict = sheets_data
        
        # 对各部分数据进行处理
        result = {}
        for key, value in data_dict.items():
            try:
                if isinstance(value, str):
                    # 尝试解析JSON数组
                    try:
                        # 移除可能的外层引号
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        
                        # 解析JSON数组
                        if value.startswith('[') and value.endswith(']'):
                            items = json.loads(value)
                        else:
                            # 如果不是JSON数组，尝试添加括号
                            items = json.loads(f'[{value}]')
                        
                        # 如果是零售商数据，按current_gmv排序并只保留前50个
                        if key == "零售商":
                            items = sorted(items, key=lambda x: float(x.get('current_gmv', 0)), reverse=True)
                            items = items[:50]
                        
                        # 构建表格格式
                        headers = ["名称", "基准GMV", "当前GMV", "GMV变化值", "GMV变化率(%)"]
                        rows = []
                        for item in items:
                            try:
                                name = item.get('name', '')
                                base_gmv = float(item.get('base_gmv', 0))
                                current_gmv = float(item.get('current_gmv', 0))
                                gmv_change = float(item.get('gmv_change', 0))
                                gmv_change_rate = float(item.get('gmv_change_rate', 0))
                                
                                rows.append([
                                    name,
                                    round(base_gmv, 2),
                                    round(current_gmv, 2),
                                    round(gmv_change, 2),
                                    round(gmv_change_rate, 2)
                                ])
                            except Exception as item_e:
                                print(f"处理单个项目时出错: {str(item_e)}")
                                continue
                        
                        result[key] = {
                            "headers": headers,
                            "rows": rows
                        }
                    except Exception as parse_e:
                        print(f"解析'{key}'的值时出错: {str(parse_e)}")
                        # 保留原始值
                        result[key] = value
                else:
                    # 非字符串值直接保留
                    result[key] = value
            except Exception as key_e:
                print(f"处理键'{key}'时出错: {str(key_e)}")
                continue
        
        # 转换为JSON字符串
        return json.dumps(result, ensure_ascii=False)
    except Exception as e:
        print(f"优化数据格式失败: {str(e)}")
        # 如果处理失败，返回原始数据
        return sheets_data

def parse_example_data_format(data_str):
    try:
        # 移除外层引号
        if data_str.startswith("'") and data_str.endswith("'"):
            data_str = data_str[1:-1]
        
        # 提取各个维度数据
        result = {}
        
        # 解析各部分数据
        import re
        
        # 匹配格式: "维度名":"[{...}]"
        pattern = r'"([^"]+)":"(\[.*?\])(?=","|"}|$)'
        matches = re.findall(pattern, data_str)
        
        if not matches:
            print("无法匹配到维度数据，重新尝试其他匹配模式")
            # 尝试不同的匹配模式
            pattern2 = r'"([^"]+)":\[({.*?})\]'
            matches = re.findall(pattern2, data_str)
            
            if matches:
                for key, items in matches:
                    # 将单个项目解析为数组
                    items_array = "[" + items + "]"
                    result[key] = items_array
        else:
            for key, value in matches:
                result[key] = value
        
        # 如果没有匹配到任何内容，尝试直接处理整个字符串
        if not result:
            print("尝试直接处理整个字符串")
            
            # 尝试转换为有效的JSON格式
            # 步骤1: 确保所有键名有双引号
            data_str = re.sub(r'(?<={|,)(\w+):', r'"\1":', data_str)
            
            # 步骤2: 处理内部的JSON数组
            # 将内嵌的JSON字符串中的双引号问题修复
            data_str = data_str.replace(':"[{', ':[{').replace('}]"', '}]')
            
            try:
                print(f"尝试解析修正后的数据: {data_str[:50]}...")
                parsed_data = json.loads(data_str)
                return parsed_data
            except Exception as e:
                print(f"直接解析失败: {str(e)}")
        
        # 格式化不同维度的数据
        formatted_result = {}
        for key, value in result.items():
            try:
                # 移除可能的外层引号
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                
                # 确保值是有效的JSON数组格式
                if not value.startswith('['):
                    value = '[' + value
                if not value.endswith(']'):
                    value = value + ']'
                
                # 修复内部JSON对象的格式问题
                value = value.replace('"{', '{').replace('}"', '}')
                
                # 解析为Python对象
                items = json.loads(value)
                
                # 限制零售商数据
                if key == "零售商" and len(items) > 50:
                    items = sorted(items, key=lambda x: float(x.get('current_gmv', 0)), reverse=True)
                    items = items[:50]
                
                # 构建表格格式
                headers = ["名称", "基准GMV", "当前GMV", "GMV变化值", "GMV变化率(%)"]
                rows = []
                
                for item in items:
                    name = item.get('name', '')
                    base_gmv = float(item.get('base_gmv', 0))
                    current_gmv = float(item.get('current_gmv', 0))
                    gmv_change = float(item.get('gmv_change', 0))
                    gmv_change_rate = float(item.get('gmv_change_rate', 0))
                    
                    rows.append([
                        name,
                        round(base_gmv, 2),
                        round(current_gmv, 2),
                        round(gmv_change, 2),
                        round(gmv_change_rate, 2)
                    ])
                
                formatted_result[key] = {
                    "headers": headers,
                    "rows": rows
                }
            except Exception as e:
                print(f"处理维度'{key}'时出错: {str(e)}")
                formatted_result[key] = value
        
        return formatted_result
    except Exception as e:
        print(f"解析示例数据格式失败: {str(e)}")
        return None

@app.route('/')
def index():
    """加载前端页面"""
    return render_template('index.html')

@app.route('/get_coupon_mechanisms', methods=['GET'])
def get_coupon_mechanisms():
    """返回券机制列表"""
    # 获取日期参数
    tar_date = request.args.get('tar_date')
    base_date = request.args.get('base_date')
    tar_start_date = request.args.get('tar_start_date')
    tar_end_date = request.args.get('tar_end_date')
    base_start_date = request.args.get('base_start_date')
    base_end_date = request.args.get('base_end_date')
    brand = request.args.get('brand')  # 品牌参数
    
    # 获取其他维度参数
    upc = request.args.get('upc', '全部')
    platform = request.args.get('platform', '全部')
    sub_brand = request.args.get('sub_brand', '全部')
    province = request.args.get('province', '全部')
    city = request.args.get('city', '全部')  # 新增城市参数
    vender = request.args.get('vender', '全部')
    coupon_threshold = request.args.get('coupon_threshold', '全部')
    coupon_discount = request.args.get('coupon_discount', '全部')
    attr_index = request.args.get('attr_index')
    
    # 活动GMV才需要券机制，全量GMV返回空列表
    if attr_index == '全量GMV':
        return jsonify([])
    else:
        # 根据日期参数获取券机制列表
        if tar_date and base_date:
            # 单日期处理逻辑
            df = get_coupon_mechanisms_for_single_date_activity(tar_date, base_date, brand, sub_brand, province, city, vender, coupon_threshold, coupon_discount, platform, upc)
            coupon_mechanisms = df['coupon_name'].tolist()
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            df = get_coupon_mechanisms_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, city, vender, coupon_threshold, coupon_discount, platform, upc)
            coupon_mechanisms = df['coupon_name'].tolist()
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(coupon_mechanisms)

@app.route('/get_coupon_threshold', methods=['GET'])
def get_coupon_threshold():
    """返回券门槛列表"""
    # 获取日期参数
    tar_date = request.args.get('tar_date')
    base_date = request.args.get('base_date')
    tar_start_date = request.args.get('tar_start_date')
    tar_end_date = request.args.get('tar_end_date')
    base_start_date = request.args.get('base_start_date')
    base_end_date = request.args.get('base_end_date')
    brand = request.args.get('brand')  # 品牌参数
    
    # 获取其他维度参数
    upc = request.args.get('upc', '全部')
    platform = request.args.get('platform', '全部')
    sub_brand = request.args.get('sub_brand', '全部')
    province = request.args.get('province', '全部')
    city = request.args.get('city', '全部')  # 新增城市参数
    vender = request.args.get('vender', '全部')
    coupon_mechanism = request.args.get('coupon_mechanism', '全部')
    coupon_discount = request.args.get('coupon_discount', '全部')
    attr_index = request.args.get('attr_index')
    
    # 活动GMV才需要券门槛，全量GMV返回空列表
    if attr_index == '全量GMV':
        return jsonify([])
    else:
        # 根据日期参数获取券门槛列表
        if tar_date and base_date:
            # 单日期处理逻辑
            df = get_coupon_threshold_for_single_date_activity(tar_date, base_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_discount, platform, upc)
            coupon_thresholds = df['threshold'].tolist()
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            df = get_coupon_threshold_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_discount, platform, upc)
            coupon_thresholds = df['threshold'].tolist()
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(coupon_thresholds)

@app.route('/get_coupon_discount', methods=['GET'])
def get_coupon_discount():
    """返回券折扣列表"""
    # 获取日期参数
    tar_date = request.args.get('tar_date')
    base_date = request.args.get('base_date')
    tar_start_date = request.args.get('tar_start_date')
    tar_end_date = request.args.get('tar_end_date')
    base_start_date = request.args.get('base_start_date')
    base_end_date = request.args.get('base_end_date')
    brand = request.args.get('brand')  # 品牌参数
    
    # 获取其他维度参数
    upc = request.args.get('upc', '全部')
    platform = request.args.get('platform', '全部')
    sub_brand = request.args.get('sub_brand', '全部')
    province = request.args.get('province', '全部')
    city = request.args.get('city', '全部')  # 新增城市参数
    vender = request.args.get('vender', '全部')
    coupon_mechanism = request.args.get('coupon_mechanism', '全部')
    coupon_threshold = request.args.get('coupon_threshold', '全部')
    attr_index = request.args.get('attr_index')
    
    # 活动GMV才需要券折扣，全量GMV返回空列表
    if attr_index == '全量GMV':
        return jsonify([])
    else:
        # 根据日期参数获取券折扣列表
        if tar_date and base_date:
            # 单日期处理逻辑
            df = get_coupon_discount_for_single_date_activity(tar_date, base_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, platform, upc)
            coupon_discounts = df['discount'].tolist()
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            df = get_coupon_discount_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, platform, upc)
            coupon_discounts = df['discount'].tolist()
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(coupon_discounts)

@app.route('/get_sub_brands', methods=['GET'])
def get_sub_brands():
    """返回子品牌列表"""
    # 获取日期参数
    tar_date = request.args.get('tar_date')
    base_date = request.args.get('base_date')
    tar_start_date = request.args.get('tar_start_date')
    tar_end_date = request.args.get('tar_end_date')
    base_start_date = request.args.get('base_start_date')
    base_end_date = request.args.get('base_end_date')
    brand = request.args.get('brand')  # 品牌参数
    
    # 获取其他维度参数
    upc = request.args.get('upc', '全部')
    platform = request.args.get('platform', '全部')
    province = request.args.get('province', '全部')
    city = request.args.get('city', '全部')  # 新增城市参数
    vender = request.args.get('vender', '全部')
    coupon_mechanism = request.args.get('coupon_mechanism', '全部')
    coupon_threshold = request.args.get('coupon_threshold', '全部')
    coupon_discount = request.args.get('coupon_discount', '全部')
    attr_index = request.args.get('attr_index')
    
    if attr_index == '全量GMV':
        # 根据日期参数获取子品牌列表（全量GMV）
        if tar_date and base_date:
            # 单日期处理逻辑
            datetime.strptime(tar_date, "%Y%m%d")
            tar_date = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
            base_date = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
            
            # 处理单日日期逻辑
            df = get_sub_brands_for_single_date_gmv(tar_date, base_date, brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            sub_brands = df['sub_brand'].tolist()
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            datetime.strptime(tar_start_date, "%Y%m%d")
            datetime.strptime(tar_end_date, "%Y%m%d")
            tar_start_date = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
            tar_end_date = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
            base_start_date = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
            base_end_date = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
            
            df = get_sub_brands_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            sub_brands = df['sub_brand'].tolist()
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400
        
        return jsonify(sub_brands)
    else:
        # 根据日期参数获取子品牌列表（活动GMV）
        if tar_date and base_date:
            # 单日期处理逻辑
            df = get_sub_brands_for_single_date_activity(tar_date, base_date, brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            sub_brands = df['sub_brand'].tolist()
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            df = get_sub_brands_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            sub_brands = df['sub_brand'].tolist()
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(sub_brands)

@app.route('/get_provinces', methods=['GET'])
def get_provinces():
    """返回省份列表"""
    # 获取日期参数
    tar_date = request.args.get('tar_date')
    base_date = request.args.get('base_date')
    tar_start_date = request.args.get('tar_start_date')
    tar_end_date = request.args.get('tar_end_date')
    base_start_date = request.args.get('base_start_date')
    base_end_date = request.args.get('base_end_date')
    brand = request.args.get('brand')  # 品牌参数
    
    # 获取其他维度参数
    upc = request.args.get('upc', '全部')
    platform = request.args.get('platform', '全部')
    sub_brand = request.args.get('sub_brand', '全部')
    city = request.args.get('city', '全部')  # 新增城市参数
    vender = request.args.get('vender', '全部')
    coupon_mechanism = request.args.get('coupon_mechanism', '全部')
    coupon_threshold = request.args.get('coupon_threshold', '全部')
    coupon_discount = request.args.get('coupon_discount', '全部')
    attr_index = request.args.get('attr_index')
    
    if attr_index == '全量GMV':
        # 根据日期参数获取省份列表
        if tar_date and base_date:
            # 单日期处理逻辑
            datetime.strptime(tar_date, "%Y%m%d")
            tar_date = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
            base_date = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
            
            df = get_provinces_for_single_date_gmv(tar_date, base_date, brand, sub_brand, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            provinces = df['province'].tolist()
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            datetime.strptime(tar_start_date, "%Y%m%d")
            datetime.strptime(tar_end_date, "%Y%m%d")
            tar_start_date = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
            tar_end_date = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
            base_start_date = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
            base_end_date = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
            
            df = get_provinces_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            provinces = df['province'].tolist()
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(provinces)
    else:
        # 根据日期参数获取省份列表（活动GMV）
        if tar_date and base_date:
            # 单日期处理逻辑
            df = get_provinces_for_single_date_activity(tar_date, base_date, brand, sub_brand, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            provinces = df['province'].tolist()
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            df = get_provinces_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            provinces = df['province'].tolist()
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(provinces)

@app.route('/get_retailers', methods=['GET'])
def get_retailers():
    """返回零售商列表"""
    # 获取日期参数
    tar_date = request.args.get('tar_date')
    base_date = request.args.get('base_date')
    tar_start_date = request.args.get('tar_start_date')
    tar_end_date = request.args.get('tar_end_date')
    base_start_date = request.args.get('base_start_date')
    base_end_date = request.args.get('base_end_date')
    brand = request.args.get('brand')  # 品牌参数
    
    # 获取其他维度参数
    upc = request.args.get('upc', '全部')
    platform = request.args.get('platform', '全部')
    sub_brand = request.args.get('sub_brand', '全部')
    province = request.args.get('province', '全部')
    city = request.args.get('city', '全部')  # 新增城市参数
    coupon_mechanism = request.args.get('coupon_mechanism', '全部')
    coupon_threshold = request.args.get('coupon_threshold', '全部')
    coupon_discount = request.args.get('coupon_discount', '全部')
    attr_index = request.args.get('attr_index')
    
    if attr_index == '全量GMV':
        # 根据日期参数获取零售商列表（全量GMV）
        if tar_date and base_date:
            # 单日期处理逻辑
            datetime.strptime(tar_date, "%Y%m%d")
            tar_date = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
            base_date = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
            
            # 处理单日日期逻辑
            df = get_retailers_for_single_date_gmv(tar_date, base_date, brand, sub_brand, province, city, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            retailers = df['vender_name'].tolist()
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            datetime.strptime(tar_start_date, "%Y%m%d")
            datetime.strptime(tar_end_date, "%Y%m%d")
            tar_start_date = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
            tar_end_date = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
            base_start_date = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
            base_end_date = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
            
            df = get_retailers_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, city, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            retailers = df['vender_name'].tolist()
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(retailers)
    else:
        # 根据日期参数获取零售商列表（活动GMV）
        if tar_date and base_date:
            # 单日期处理逻辑
            df = get_retailers_for_single_date_activity(tar_date, base_date, brand, sub_brand, province, city, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            retailers = df['vender_name'].tolist()
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            df = get_retailers_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, city, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
            retailers = df['vender_name'].tolist()
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(retailers)

@app.route('/get_platform', methods=['GET'])
def get_platform():
    """返回平台列表"""
    tar_date = request.args.get('tar_date')
    base_date = request.args.get('base_date')
    tar_start_date = request.args.get('tar_start_date')
    tar_end_date = request.args.get('tar_end_date')
    base_start_date = request.args.get('base_start_date')
    base_end_date = request.args.get('base_end_date')
    brand = request.args.get('brand')  # 品牌参数
    # 获取其他维度参数
    upc = request.args.get('upc', '全部')
    sub_brand = request.args.get('sub_brand', '全部')
    vender = request.args.get('vender', '全部')
    province = request.args.get('province', '全部')
    city = request.args.get('city', '全部')  # 新增城市参数
    coupon_mechanism = request.args.get('coupon_mechanism', '全部')
    coupon_threshold = request.args.get('coupon_threshold', '全部')
    coupon_discount = request.args.get('coupon_discount', '全部')
    attr_index = request.args.get('attr_index')
    
    if attr_index == '全量GMV':
        # 根据日期参数获取平台列表（全量GMV）
        if tar_date and base_date:
            # 单日期处理逻辑
            datetime.strptime(tar_date, "%Y%m%d")
            tar_date = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
            base_date = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
            
            # 处理单日日期逻辑
            df = get_platform_for_single_date_gmv(tar_date, base_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, upc)
            platforms = df['platform'].tolist()
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            datetime.strptime(tar_start_date, "%Y%m%d")
            datetime.strptime(tar_end_date, "%Y%m%d")
            tar_start_date = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
            tar_end_date = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
            base_start_date = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
            base_end_date = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
            
            df = get_platform_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, upc)
            platforms = df['platform'].tolist()
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(platforms)
    else:
        # 根据日期参数获取平台列表（活动GMV）
        if tar_date and base_date:
            # 单日期处理逻辑
            df = get_platform_for_single_date_activity(tar_date, base_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, upc)
            platforms = df['platform'].tolist()
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            df = get_platform_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, upc)
            platforms = df['platform'].tolist()
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(platforms)

@app.route('/get_products', methods=['GET'])
def get_products():
    """返回UPC/商品名称列表"""
    # 获取日期参数
    tar_date = request.args.get('tar_date')
    base_date = request.args.get('base_date')
    tar_start_date = request.args.get('tar_start_date')
    tar_end_date = request.args.get('tar_end_date')
    base_start_date = request.args.get('base_start_date')
    base_end_date = request.args.get('base_end_date')
    brand = request.args.get('brand')  # 品牌参数
    
    # 获取其他维度参数
    platform = request.args.get('platform', '全部')
    sub_brand = request.args.get('sub_brand', '全部')
    province = request.args.get('province', '全部')
    city = request.args.get('city', '全部')
    vender = request.args.get('vender', '全部')
    coupon_mechanism = request.args.get('coupon_mechanism', '全部')
    coupon_threshold = request.args.get('coupon_threshold', '全部')
    coupon_discount = request.args.get('coupon_discount', '全部')
    attr_index = request.args.get('attr_index')
    
    if attr_index == '全量GMV':
        # 根据日期参数获取商品名称列表（全量GMV）
        if tar_date and base_date:
            # 单日期处理逻辑
            datetime.strptime(tar_date, "%Y%m%d")
            tar_date = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
            base_date = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
            
            # 处理单日日期逻辑
            df = get_products_for_single_date_gmv(tar_date, base_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform)
            products = df.to_dict('records')
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            datetime.strptime(tar_start_date, "%Y%m%d")
            datetime.strptime(tar_end_date, "%Y%m%d")
            tar_start_date = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
            tar_end_date = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
            base_start_date = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
            base_end_date = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
            
            df = get_products_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand,  province,city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform)
            products = df.to_dict('records')
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(products)
    else:
        # 根据日期参数获取商品名称列表（活动GMV）
        if tar_date and base_date:
            # 单日期处理逻辑
            df = get_products_for_single_date_activity(tar_date, base_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform)
            products = df.to_dict('records')
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            # 处理连续日期逻辑
            df = get_products_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform)
            products = df.to_dict('records')
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400

        return jsonify(products)


@app.route('/get_city', methods=['GET'])
def get_city():
    """返回城市列表"""
    try:
        # 获取日期参数
        tar_date = request.args.get('tar_date')
        base_date = request.args.get('base_date')
        tar_start_date = request.args.get('tar_start_date')
        tar_end_date = request.args.get('tar_end_date')
        base_start_date = request.args.get('base_start_date')
        base_end_date = request.args.get('base_end_date')
        brand = request.args.get('brand')
        
        # 获取其他维度参数
        upc = request.args.get('upc', '全部')
        platform = request.args.get('platform', '全部')
        sub_brand = request.args.get('sub_brand', '全部')
        province = request.args.get('province', '全部')
        vender = request.args.get('vender', '全部')
        coupon_mechanism = request.args.get('coupon_mechanism', '全部')
        coupon_threshold = request.args.get('coupon_threshold', '全部')
        coupon_discount = request.args.get('coupon_discount', '全部')
        attr_index = request.args.get('attr_index')
        
        if attr_index == '全量GMV':
            # 根据日期参数获取城市列表（全量GMV）
            if tar_date and base_date:
                # 单日期处理逻辑
                datetime.strptime(tar_date, "%Y%m%d")
                tar_date = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
                base_date = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
                
                # 处理单日日期逻辑
                df = get_cities_for_single_date_gmv(tar_date, base_date, brand, sub_brand, province, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
                cities = df['standard_city'].tolist()
            elif tar_start_date and tar_end_date and base_start_date and base_end_date:
                # 处理连续日期逻辑
                datetime.strptime(tar_start_date, "%Y%m%d")
                datetime.strptime(tar_end_date, "%Y%m%d")
                tar_start_date = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
                tar_end_date = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
                base_start_date = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
                base_end_date = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
                
                df = get_cities_for_date_range_gmv(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
                cities = df['standard_city'].tolist()
            else:
                return jsonify({"status": "error", "message": "日期参数不完整"}), 400

            return jsonify(cities)
        else:
            # 根据日期参数获取城市列表（活动GMV）
            if tar_date and base_date:
                # 单日期处理逻辑
                tar_date = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
                base_date = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
                
                # 处理单日日期逻辑
                df = get_cities_for_single_date_activity(tar_date, base_date, brand, sub_brand, province, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
                cities = df['city'].tolist()
            elif tar_start_date and tar_end_date and base_start_date and base_end_date:
                # 处理连续日期逻辑
                tar_start_date = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
                tar_end_date = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
                base_start_date = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
                base_end_date = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
                
                df = get_cities_for_date_range_activity(tar_start_date, tar_end_date, base_start_date, base_end_date, brand, sub_brand, province, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
                cities = df['city'].tolist()
            else:
                return jsonify({"status": "error", "message": "日期参数不完整"}), 400

            return jsonify(cities)
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/run_attribution', methods=['POST'])
def run_attribution():
    """处理归因分析逻辑"""
    try:
        attr_index = request.form.get('attr_index')
        file_path = None
        sheets_data = None
        analysis_dimensions = request.form.get('analysis_dimensions')
        analysis_dimensions = analysis_dimensions.replace('商品名称', '商品')
        if analysis_dimensions:
            analysis_dimensions_list = [dim.strip() for dim in analysis_dimensions.split(',')]
        else:
            analysis_dimensions_list = []
        
        # 处理交叉维度数据
        cross_dimension_enabled = request.form.get('cross_dimension_enabled', 'false').lower() == 'true'
        dimension_combinations_str = request.form.get('dimension_combinations', '[]')
        cross_dimension_combinations = process_cross_dimension_combinations(dimension_combinations_str, attr_index)
        
        # 获取基础参数
        brand = request.form.get('brand')
        sub_brand = request.form.get('sub_brand', '全部')
        province = request.form.get('province', '全部')
        city = request.form.get('city', '全部')
        vender = request.form.get('retailer', '全部')
        platform = request.form.get('platform', '全部')
        upc = request.form.get('upc', '全部')
        
        # 获取日期参数并确定flag
        tar_date = request.form.get('tar_date')
        base_date = request.form.get('base_date')
        tar_start_date = request.form.get('tar_start_date')
        tar_end_date = request.form.get('tar_end_date')
        base_start_date = request.form.get('base_start_date')
        base_end_date = request.form.get('base_end_date')
        
        # 确定flag值：1表示单日期，2表示日期范围
        if tar_date and base_date:
            flag = 1
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            flag = 2
        else:
            flag = 1  # 默认为单日期模式
        
        # 检查请求是否为 JSON
        if request.is_json:
            data = request.get_json()
            file_path = data.get('file_path')
        
        # 如果是表单数据
        elif request.form:
            file_path = request.form.get('file_path')
        
        # 如果是查询参数
        elif request.args:
            file_path = request.args.get('file_path')

        # 如果提供了文件路径，优先读取文件内容
        if file_path and os.path.exists(file_path):
            try:
                # 读取Excel文件内容
                import pandas as pd
                sheets_data = {}
                sheets_data_for_ai = {}  # 用于AI归因分析的原始数据结构
                # excel_file = pd.ExcelFile(file_path)
                keep_columns = analysis_dimensions_list
                # if attr_index == '全量GMV':

                #     keep_columns = ['平台', '省份', '城市', '零售商', '子品牌', '商品']
                # elif attr_index == '活动GMV':
                #     keep_columns = ['平台', '省份', '城市', '零售商', '子品牌', '券机制', '券门槛', '优惠力度']
                # else:
                #     return jsonify({"status": "error", "message": "无效的attr_index"}), 400
                
                # 将英文维度名称映射为中文工作表名称
                english_to_chinese_mapping = {
                    "platform": "平台",
                    "province": "省份", 
                    "standard_city": "城市",
                    "vender_name": "零售商",
                    "sub_brand": "子品牌",
                    "product_name": "商品",
                    "coupon_name": "券机制",
                    "coupon_threshold": "券门槛",
                    "coupon_discount": "优惠力度"
                }
                
                # 将英文维度名转换为中文工作表名
                chinese_sheet_names = []
                for english_name in keep_columns:
                    chinese_name = english_to_chinese_mapping.get(english_name, english_name)
                    chinese_sheet_names.append(chinese_name)
                
                print(f"要读取的工作表: {chinese_sheet_names}")
                
                # attr_index已经在函数开始时定义了
                for sheet_name in chinese_sheet_names:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    
                    # 排除 source 和内容列
                    exclude_columns = ['source']
                    df = df.drop(columns=[col for col in exclude_columns if col in df.columns])
                    
                    # 先计算 top10、bottom10 和 change_rate_top_10（基于原始完整数据）
                    # 确保GMV贡献度列是数值类型
                    if 'GMV贡献度' in df.columns:
                        def convert_percentage(value):
                            if pd.isna(value):
                                return 0
                            if isinstance(value, str) and value.endswith('%'):
                                try:
                                    return float(value.rstrip('%')) / 100
                                except:
                                    return 0
                            try:
                                return float(value)
                            except:
                                return 0
                        
                        df['GMV贡献度'] = df['GMV贡献度'].apply(convert_percentage)
                    
                    # 按GMV贡献度排序获取TOP10和BOTTOM10
                    df_sorted_by_contribution = df.sort_values(by='GMV贡献度', ascending=False)
                    
                    # 调试信息：打印前5行的GMV贡献度值
                    print(f"Sheet: {sheet_name}")
                    print("前5行GMV贡献度值:")
                    for i, row in df_sorted_by_contribution.head(5).iterrows():
                        print(f"  {row.iloc[0]}: {row['GMV贡献度']}")
                    
                    top_10 = df_sorted_by_contribution.head(10).to_dict('records')
                    bottom_10 = df_sorted_by_contribution.tail(10).to_dict('records')
                    
                    # 按变化率绝对值排序取前十（如果存在变化率列）
                    change_rate_top_10 = []
                    if 'GMV变化率' in df.columns:
                        # 确保GMV变化率列是数值类型
                        def convert_change_rate(value):
                            if pd.isna(value):
                                return 0
                            if isinstance(value, str):
                                # 移除%符号和+号
                                value_str = value.replace('%', '').replace('+', '')
                                try:
                                    return float(value_str) / 100
                                except:
                                    return 0
                            try:
                                return float(value)
                            except:
                                return 0

                        # 创建数值版本的变化率列用于排序
                        df_temp = df.copy()
                        df_temp['GMV变化率_数值'] = df_temp['GMV变化率'].apply(convert_change_rate)

                        # 按GMV变化率绝对值降序排序，取前10
                        df_sorted_by_change_rate = df_temp.reindex(df_temp['GMV变化率_数值'].abs().sort_values(ascending=False).index)
                        change_rate_top_10 = df_sorted_by_change_rate.head(10).to_dict('records')

                    # 将GMV贡献度转换回百分比格式（用于传入get_attributional_result_text函数）
                    if 'GMV贡献度' in df_sorted_by_contribution.columns:
                        df_sorted_by_contribution['GMV贡献度'] = df_sorted_by_contribution['GMV贡献度'].apply(
                            lambda x: f"{x*100:.2f}%" if pd.notna(x) and x != 0 else "0.00%"
                        )

                    # 为最终展示重新按GMV贡献度排序
                    df = df_sorted_by_contribution
                    
                    # 然后对特定sheet进行筛选
                    # 根据attr_index确定正确的GMV字段名
                    gmv_field = '当期GMV' if attr_index == '全量GMV' else '当期活动GMV'
                    
                    # 特殊处理零售商 sheet
                    if sheet_name == '零售商':
                        # 按 GMV 当前值降序排序，只取前 50 行
                        if gmv_field in df.columns:
                            df = df.sort_values(by=gmv_field, ascending=False).head(50)
                    if attr_index == '全量GMV':
                        # 特殊处理商品 sheet - 针对可口可乐和雪花啤酒品牌限制只获取TOP500
                        if sheet_name == '商品':
                            # 按 GMV 当前值降序排序，只取前 500 行
                            if gmv_field in df.columns:
                                df = df.sort_values(by=gmv_field, ascending=False).head(500)
                    else:
                        if sheet_name == '城市':
                            # 按 GMV 当前值降序排序，只取前 200 行
                            if gmv_field in df.columns:
                                df = df.sort_values(by=gmv_field, ascending=False).head(200)

                    # 重新获取筛选后的records（用于最终的表格数据）
                    filtered_records = df.to_dict('records')
                    
                    # 如果记录不为空，只保留字段名和数据
                    if filtered_records:
                        headers = list(filtered_records[0].keys())
                        rows = [list(record.values()) for record in filtered_records]
                        top_10_data = [list(record.values()) for record in top_10]
                        bottom_10_data = [list(record.values()) for record in bottom_10]
                        change_rate_top_10_data = [list(record.values()) for record in change_rate_top_10]
                        sheets_data[sheet_name] = {
                            "headers": headers,
                            "rows": rows,
                            "贡献度TOP10": top_10_data,
                            "贡献度BOTTOM10": bottom_10_data,
                            "变化率TOP10": change_rate_top_10_data,
                            
                        }
                
                # 处理交叉维度数据
                if cross_dimension_enabled and cross_dimension_combinations:
                    try:
                        # 根据flag处理日期参数
                        if flag == 1:
                            # 单日期模式
                            if tar_date and base_date:
                                # 转换为yyyy-mm-dd格式
                                tar_date_fmt = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
                                base_date_fmt = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
                            else:
                                tar_date_fmt = None
                                base_date_fmt = None
                        else:
                            # 日期范围模式
                            if tar_start_date and tar_end_date and base_start_date and base_end_date:
                                tar_date_fmt = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
                                base_date_fmt = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
                            else:
                                tar_date_fmt = None
                                base_date_fmt = None
                        
                        # 使用数据库查询获取交叉维度数据
                        cross_results = get_cross_dimension_attribution_result(
                            flag=flag,
                            brand=brand,
                            tar_date=tar_date_fmt,
                            base_date=base_date_fmt,
                            sub_brand=sub_brand,
                            province=province,
                            city=city,
                            vender=vender,
                            cross_dimension_combinations=cross_dimension_combinations,
                            platform=platform,
                            upc=upc
                        )
                        
                        # 将交叉维度结果转换为前端需要的格式
                        for combo_name, cross_data in cross_results.items():
                            if cross_data:
                                # 转换为headers和rows格式
                                headers = list(cross_data[0].keys()) if cross_data else []
                                rows = [list(record.values()) for record in cross_data]
                                
                                sheets_data[combo_name] = {
                                    "headers": headers,
                                    "rows": rows,
                                    "贡献度TOP10": rows[:10],
                                    "贡献度BOTTOM10": rows[-10:] if len(rows) > 10 else [],
                                    "变化率TOP10": rows[:10]
                                }
                                print(f"交叉维度 {combo_name} 数据已添加到结果中")
                    except Exception as e:
                        print(f"处理交叉维度数据时出错: {e}")
                        import traceback
                        traceback.print_exc()
                
                # 保存原始sheets_data用于AI归因分析
                sheets_data_for_ai = sheets_data.copy()

                # 将sheets_data转换为JSON字符串
                sheets_data = json.dumps(sheets_data, ensure_ascii=False)

            except Exception as e:
                print(f"读取Excel文件失败: {str(e)}")
                        # 如果是供给向指标分析，直接返回成功（因为数据已经在 get_result_data 中提供了）
        # AI归因结论只使用维度归因，以下变量已注释
        # attribution_methods = request.form.get('attribution_methods', '')
        # indicator_types = request.form.get('indicator_types', '')
        # # 修复：不依赖顺序，检查是否同时包含"维度"和"指标"
        # has_dimension = "维度" in attribution_methods
        # has_indicator = "指标" in attribution_methods

        # 使用新的辅助函数检查指标类型 - AI归因结论只使用维度归因，已注释
        # has_supply_side = has_supply_side_indicator(indicator_types)
        # has_marketing_side = has_marketing_side_indicator(indicator_types)

        # 判断是否为供给向指标（需要有指标分析且包含供给向） - AI归因结论只使用维度归因，已注释
        # is_supply_side = has_dimension and has_indicator and has_supply_side

        # 处理不同类型的归因分析
        # 注释：AI归因结论只使用维度归因分析，其他归因方法已注释
        # if is_supply_side and not has_marketing_side:
        #     # 纯供给向指标分析
        #     attribution_result = get_supply_side_attribution_text(sheets_data)
        #     return jsonify({
        #         "status": "success",
        #         "attribution_result": attribution_result,
        #         "message": "供给向指标分析完成"
        #     })
        # elif has_supply_side and has_marketing_side:
        #     # 同时包含供给向和营销向指标
        #     attribution_result = get_mixed_attribution_text(sheets_data)
        #     return jsonify({
        #         "status": "success",
        #         "attribution_result": attribution_result,
        #         "message": "混合指标分析完成"
        #     })
        # else:
        #     # 传统维度归因分析
        #     attribution_result = get_attributional_result_text(sheets_data)
        #     return jsonify({
        #         "status": "success",
        #         "attribution_result": attribution_result
        #     })

        # 只保留维度归因分析 - 使用原始数据结构而不是JSON字符串
        attribution_result = get_attributional_result_text(sheets_data_for_ai)
        return jsonify({
            "status": "success",
            "attribution_result": attribution_result
        })
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"错误详情: {error_details}")
        return jsonify({"status": "error", "message": str(e), "details": error_details}), 500

        

@app.route('/get_result_data', methods=['GET'])
def get_result_data():
    """返回不同sheets的top3_results和sheets_data"""
    try:
        # 使用新的参数处理函数来处理多选参数
        params = process_multi_select_params(request.args)
        brand = params['brand']
        sub_brand = params['sub_brand']
        province = params['province']
        city = params['city']
        vender = params['retailer']
        attr_index = params['attr_index']
        coupon_mechanism = params['coupon_mechanism']
        coupon_threshold = params['coupon_threshold']
        coupon_discount = params['coupon_discount']
        platform = params['platform']
        upc = params['upc']
        analysis_dimensions = params['analysis_dimensions']
        
        # 将前端传入的中文维度转换为英文维度列表
        analysis_dimensions_list = convert_analysis_dimensions(analysis_dimensions, attr_index)
        
        # 获取新增的归因方法和指标类型参数
        attribution_methods = params['attribution_methods']
        indicator_types = params['indicator_types']
        
        # 处理交叉维度数据
        cross_dimension_enabled = params['cross_dimension_enabled']
        dimension_combinations_str = params['dimension_combinations']
        cross_dimension_combinations = process_cross_dimension_combinations(dimension_combinations_str, attr_index)
        
        # 根据日期类型获取日期
        if params['tar_date']:
            flag = 1
            tar_date = params['tar_date']
            try:
                datetime.strptime(tar_date, "%Y%m%d")
            except ValueError:
                return jsonify({"status": "error", "message": "日期格式错误，请使用 YYYYMMDD 格式"})

            # 要求前端必须传递base_date参数
            base_date = params['base_date']
            if not base_date:
                return jsonify({"status": "error", "message": "缺少对比日期参数"})
            
            try:
                datetime.strptime(base_date, "%Y%m%d")
            except ValueError:
                return jsonify({"status": "error", "message": "对比日期格式错误，请使用 YYYYMMDD 格式"})
                
            # 对全量GMV转换日期格式
            if attr_index == '全量GMV':
                # 转换为yyyy-mm-dd格式
                tar_date_fmt = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
                base_date_fmt = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
                
                gmv_change_result = get_gmv_change_result_single_date(brand, tar_date_fmt, base_date_fmt, sub_brand, province, city, vender, platform, upc)
            elif attr_index == '活动GMV':
                # 转换为yyyy-mm-dd格式
                tar_date_fmt = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
                base_date_fmt = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
                
                gmv_change_result = get_activity_gmv_change_result_single_date(brand, tar_date_fmt, base_date_fmt, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)

        else:
            flag = 2
            tar_start_date = params['tar_start_date']
            tar_end_date = params['tar_end_date']
            base_start_date = params['base_start_date']
            base_end_date = params['base_end_date']

            for date in [tar_start_date, tar_end_date, base_start_date, base_end_date]:
                try:
                    datetime.strptime(date, "%Y%m%d")
                except ValueError:
                    return jsonify({"status": "error", "message": "日期格式错误，请使用 YYYYMMDD 格式"})
            if attr_index == '全量GMV':
                # 转换为yyyy-mm-dd格式
                tar_start_date_fmt = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
                tar_end_date_fmt = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
                base_start_date_fmt = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
                base_end_date_fmt = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
                
                # 添加检查：目标日期范围和基准日期范围是否相同
                if tar_start_date_fmt == base_start_date_fmt and tar_end_date_fmt == base_end_date_fmt:
                    return jsonify({
                        "status": "time_fail",
                        "message": "目标日期范围和对比日期范围不能相同，请重新选择！"
                    })
                
                tar_date_fmt = f"{tar_start_date_fmt}至{tar_end_date_fmt}"
                base_date_fmt = f"{base_start_date_fmt}至{base_end_date_fmt}"
                
                gmv_change_result = get_gmv_change_result_multi_date(brand, tar_start_date_fmt, tar_end_date_fmt, base_start_date_fmt, base_end_date_fmt, sub_brand, province, city, vender, platform, upc)
            elif attr_index == '活动GMV':
                # 转换为yyyy-mm-dd格式
                tar_start_date_fmt = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
                tar_end_date_fmt = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
                base_start_date_fmt = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
                base_end_date_fmt = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
                
                # 添加检查：目标日期范围和基准日期范围是否相同
                if tar_start_date_fmt == base_start_date_fmt and tar_end_date_fmt == base_end_date_fmt:
                    return jsonify({
                        "status": "time_fail",
                        "message": "目标日期范围和对比日期范围不能相同，请重新选择！"
                    })
                
                tar_date_fmt = f"{tar_start_date_fmt}至{tar_end_date_fmt}"
                base_date_fmt = f"{base_start_date_fmt}至{base_end_date_fmt}"
                
                gmv_change_result = get_activity_gmv_change_result_multi_date(brand, tar_start_date_fmt, tar_end_date_fmt, base_start_date_fmt, base_end_date_fmt, sub_brand, province, city, vender, coupon_mechanism, coupon_threshold, coupon_discount, platform, upc)
                
        if gmv_change_result is None:
            return jsonify({
                "status": "lose",
                "message": "当前筛选维度没有数据，请重新选择！"
            })
        else:
            if None in gmv_change_result or gmv_change_result.isnull().all().all() or gmv_change_result.empty:
                return jsonify({
                    "status": "lose",
                    "message": "当前筛选维度没有数据，请重新选择！"
                })
            else:
                # 检查是目标日期还是对比日期没数据
                gmv_base = gmv_change_result.iloc[0].get('gmv_base', 0)
                gmv_target = gmv_change_result.iloc[0].get('gmv_target', 0)
                
                if pd.isna(gmv_base) or gmv_base == 0:
                    if pd.isna(gmv_target) or gmv_target == 0:
                        return jsonify({
                            "status": "lose",
                            "message": "目标日期和对比日期都没有数据，请重新选择！"
                        })
                    else:
                        return jsonify({
                            "status": "lose",
                            "message": "对比日期范围没有数据，请重新选择！"
                        })
                elif pd.isna(gmv_target) or gmv_target == 0:
                    return jsonify({
                        "status": "lose",
                        "message": "目标日期范围没有数据，请重新选择！"
                    })
                
                # 构建市场数据信息
                if attr_index == '全量GMV':
                    try:
                        gmv_base = round(gmv_change_result.iloc[0]['gmv_base'])
                        gmv_target = round(gmv_change_result.iloc[0]['gmv_target'])
                        gmv_change = round(gmv_change_result.iloc[0]['gmv_change'])
                        gmv_change_rate = gmv_change_result.iloc[0]['gmv_change_rate']*100
                        direction = "增长" if gmv_change >= 0 else "下降"
                        trim_gmv_target, trim_gmv_base, trim_gmv_change = f"{gmv_target:,}", f"{gmv_base:,}", f"{gmv_change:,}"
                        
                        # 使用转换后的显示日期
                        market_data = {
                            "gmv_target": gmv_target,
                            "gmv_base": gmv_base,
                            "gmv_change": gmv_change,
                            "gmv_change_rate": gmv_change_rate,
                            "direction": direction
                        }
                    except (TypeError, IndexError, ValueError) as e:
                        print(f"处理全量GMV数据时出错: {str(e)}")
                        return jsonify({
                            "status": "lose",
                            "message": "当前筛选维度没有数据，请重新选择！"
                        })
                elif attr_index == '活动GMV':
                    try:
                        gmv_base = round(gmv_change_result.iloc[0]['gmv_base'])
                        expense_base = round(gmv_change_result.iloc[0]['expense_base'])
                        gmv_target = round(gmv_change_result.iloc[0]['gmv_target'])
                        expense_target = round(gmv_change_result.iloc[0]['expense_target'])
                        gmv_change = round(gmv_change_result.iloc[0]['gmv_change'])
                        expense_change = round(gmv_change_result.iloc[0]['expense_change'])
                        gmv_change_rate = gmv_change_result.iloc[0]['gmv_change_rate']*100
                        expense_change_rate = gmv_change_result.iloc[0]['expense_change_rate']*100
                        gmv_direction = "增长" if gmv_change >= 0 else "下降"
                        expense_direction = "增长" if expense_change >= 0 else "下降"
                        base_roi = round(gmv_base/expense_base, 2) if expense_base > 0 else 0
                        tar_roi = round(gmv_target/expense_target, 2) if expense_target > 0 else 0
                        roi_change = round(tar_roi - base_roi, 2)
                        roi_direction = "增长" if roi_change >= 0 else "下降"
                        roi_change_rate = round(roi_change / base_roi, 4) * 100 if base_roi > 0 else 0

                        market_data = {
                            "gmv_target": gmv_target,
                            "gmv_base": gmv_base,
                            "gmv_change": gmv_change,
                            "gmv_change_rate": gmv_change_rate,
                            "gmv_direction": gmv_direction,
                            "expense_target": expense_target,
                            "expense_base": expense_base,
                            "expense_change": expense_change,
                            "expense_change_rate": expense_change_rate,
                            "expense_direction": expense_direction,
                            "roi_target": tar_roi,
                            "roi_base": base_roi,
                            "roi_change": roi_change,
                            "roi_change_rate": roi_change_rate,
                            "roi_direction": roi_direction
                        }
                    except (TypeError, IndexError, ValueError) as e:
                        print(f"处理活动GMV数据时出错: {str(e)}")
                        return jsonify({
                            "status": "lose",
                            "message": "当前筛选维度没有数据，请重新选择！"
                        })

                # 获取前端传入的对比方式参数
                compare_detail_type = params.get('compare_detail_type')
                
                # 获取历史GMV波动数据
                historical_gmv_changes = []
                if attr_index == '全量GMV':
                    historical_gmv_changes = get_historical_gmv_changes(
                            brand=brand,
                            compare_type=compare_detail_type,
                            sub_brand=sub_brand,
                            province=province,
                            city=city,  # 添加城市参数
                            vender=vender,
                            platform=platform,
                            upc=upc,
                            gmv_base = gmv_base,
                            gmv_target = gmv_target
                        )
                else:
                    historical_gmv_changes = get_historical_activity_gmv_changes(
                            brand=brand,
                            compare_type=compare_detail_type,
                            sub_brand=sub_brand,
                            province=province,
                            city=city,  # 添加城市参数
                            vender=vender,
                            coupon_mechanism=coupon_mechanism,
                            coupon_threshold=coupon_threshold,
                            coupon_discount=coupon_discount,
                            platform=platform,
                            upc=upc,
                            gmv_base = gmv_base,
                            gmv_target = gmv_target
                        )
                        # 确保数据按日期排序
                    historical_gmv_changes.sort(key=lambda x: x.get('date', ''))
                # 准备维度列表
                dim_list_out = ['sub_brand', 'province', 'standard_city', 'vender_name', 'product_name', 'coupon_name', 'coupon_threshold', 'coupon_discount']
                
                # 根据筛选条件移除已选择的维度
                if sub_brand != "全部":
                    dim_list_out.remove("sub_brand")
                if province != "全部":
                    dim_list_out.remove("province")
                if city != "全部":
                    dim_list_out.remove("standard_city")
                if vender != "全部":
                    dim_list_out.remove("vender_name")
                
                # 处理全量GMV和活动GMV特殊逻辑
                if attr_index == '全量GMV':
                    # 移除券相关维度
                    if "coupon_name" in dim_list_out:
                        dim_list_out.remove("coupon_name")
                    if "coupon_threshold" in dim_list_out:
                        dim_list_out.remove("coupon_threshold")
                    if "coupon_discount" in dim_list_out:
                        dim_list_out.remove("coupon_discount")
                    
                    # 检查日期是否相同
                    if request.args.get('tar_date') and tar_date_fmt == base_date_fmt:
                        return jsonify({
                            "status": "time_fail",
                            "message": "目标日期和对比日期不能相同，请重新选择！"
                        })
                    else:
                        # 判断指标类型
                        has_dimension = "维度" in attribution_methods
                        has_indicator = "指标" in attribution_methods
                        has_supply_side = has_supply_side_indicator(indicator_types)
                        has_marketing_side = has_marketing_side_indicator(indicator_types)
                        
                        if has_dimension and has_indicator and has_supply_side:
                            # 如果包含供给向指标，使用供给向指标逻辑
                            # get_result_data接口统一使用品牌-城市表，不根据维度选择表
                            from datadeal import get_supply_side_attribution_result
                            results_for_detail = get_supply_side_attribution_result(
                                flag, brand, tar_date_fmt, base_date_fmt,
                                sub_brand, province, city, vender, analysis_dimensions_list, platform, upc,
                                from_dimension_attribution=False
                            )
                        else:
                            # 获取传统GMV归因结果
                            if flag == 1:
                                results_for_detail = get_attibutional_result(flag, brand, tar_date_fmt, base_date_fmt, 
                                                                                sub_brand, province, city, vender, dim_list_out, platform, upc,analysis_dimensions_list)
                            else:
                                results_for_detail = get_attibutional_result(flag, brand, tar_date_fmt, base_date_fmt, 
                                                                                sub_brand, province, city, vender, dim_list_out, platform, upc,analysis_dimensions_list)
                elif attr_index == '活动GMV':
                    # 根据选择移除券相关维度
                    if coupon_mechanism != "全部" and "coupon_name" in dim_list_out:
                        dim_list_out.remove("coupon_name")
                    if coupon_threshold != "全部" and "coupon_threshold" in dim_list_out:
                        dim_list_out.remove("coupon_threshold")
                    if coupon_discount != "全部" and "coupon_discount" in dim_list_out:
                        dim_list_out.remove("coupon_discount")
                    
                    # 活动GMV不需要商品维度
                    if "product_name" in dim_list_out:
                        dim_list_out.remove("product_name")
                    
                    # 判断指标类型（活动GMV暂不支持供给向指标）
                    has_dimension = "维度" in attribution_methods
                    has_indicator = "指标" in attribution_methods
                    has_supply_side = has_supply_side_indicator(indicator_types)
                    has_marketing_side = has_marketing_side_indicator(indicator_types)
                    
                    if has_dimension and has_indicator and has_supply_side:
                        return jsonify({
                            "status": "error",
                            "message": "活动GMV暂不支持供给向指标分析，请选择全量GMV或营销向指标"
                        })
                    else:
                        # 获取活动归因结果
                        if flag == 1:
                            results_for_detail= get_activity_attibutional_result(flag, brand, tar_date_fmt, base_date_fmt, 
                                                                                   sub_brand, province, city, vender, coupon_mechanism, 
                                                                                   coupon_threshold, coupon_discount, dim_list_out, platform, upc,analysis_dimensions_list)
                        else:
                            results_for_detail= get_activity_attibutional_result(flag, brand, tar_date_fmt, base_date_fmt, 
                                                                                   sub_brand, province, city, vender, coupon_mechanism, 
                                                                                   coupon_threshold, coupon_discount, dim_list_out, platform, upc,analysis_dimensions_list)

                # 判断指标类型
                has_dimension = "维度" in attribution_methods
                has_indicator = "指标" in attribution_methods
                has_supply_side = has_supply_side_indicator(indicator_types)
                has_marketing_side = has_marketing_side_indicator(indicator_types)
                is_supply_side = has_dimension and has_indicator and has_supply_side and attr_index == '全量GMV'
                
                # 检查是否有有效数据
                if not results_for_detail or len(results_for_detail) == 0:
                    if is_supply_side:
                        return jsonify({
                            "status": "lose",
                            "message": "当前时间范围内没有供给向指标数据，请选择其他时间段或联系管理员确认数据可用性！"
                        })
                    else:
                        return jsonify({
                            "status": "lose", 
                            "message": "当前筛选维度没有数据，请重新选择！"
                        })
                
                # 生成唯一的文件名
                unique_filename = f"{uuid.uuid4()}_attribution_result.xlsx"
                unique_filepath = os.path.join(ATTRIBUTION_RESULT_DIR, unique_filename)

                # 创建完整的数据字典，包含维度归因和指标归因数据
                complete_data_for_excel = {}

                # 添加维度归因数据（确保是DataFrame格式），排除不必要的工作表
                if results_for_detail:
                    print(f"开始处理维度归因数据，包含 {len(results_for_detail)} 个维度")
                    for sheet_name, data in results_for_detail.items():
                        # 跳过overall工作表，它是供给向指标的原始数据，不需要单独的工作表
                        if sheet_name == 'overall':
                            print(f"跳过overall工作表（供给向指标原始数据）")
                            continue

                        print(f"处理维度: {sheet_name}, 数据类型: {type(data)}")
                        if isinstance(data, pd.DataFrame):
                            # 应用列名映射
                            mapped_columns = [column_name_mapping.get(col, col) for col in data.columns]
                            data_copy = data.copy()
                            data_copy.columns = mapped_columns

                            # 应用工作表名映射
                            mapped_sheet_name = sheet_name_mapping.get(sheet_name, sheet_name)
                            complete_data_for_excel[mapped_sheet_name] = data_copy
                            print(f"成功添加维度数据: {mapped_sheet_name}, 行数: {len(data_copy)}, 列数: {len(data_copy.columns)}")
                        else:
                            # 如果不是DataFrame，尝试转换
                            try:
                                df = pd.DataFrame(data)
                                if not df.empty:
                                    # 应用列名映射
                                    mapped_columns = [column_name_mapping.get(col, col) for col in df.columns]
                                    df.columns = mapped_columns

                                    # 应用工作表名映射
                                    mapped_sheet_name = sheet_name_mapping.get(sheet_name, sheet_name)
                                    complete_data_for_excel[mapped_sheet_name] = df
                                    print(f"成功转换并添加维度数据: {mapped_sheet_name}, 行数: {len(df)}, 列数: {len(df.columns)}")
                                else:
                                    print(f"维度 {sheet_name} 转换后的DataFrame为空")
                            except Exception as e:
                                print(f"转换维度归因数据失败 {sheet_name}: {e}")
                                continue
                
                # 基础结果结构
                result = {
                    "status": "success",
                    "file_path": unique_filepath,
                    "analysis_type": {
                        "attribution_methods": attribution_methods,
                        "indicator_types": indicator_types,
                        "has_supply_side": has_supply_side,
                        "has_marketing_side": has_marketing_side,
                        "is_supply_side": is_supply_side,
                        "is_mixed_indicators": has_supply_side and has_marketing_side
                    }
                }
                
                # 维度归因数据处理 - 无论是否选择维度归因方法，都需要为前端显示和Excel下载提供维度数据
                if "维度" in attribution_methods or True:  # 总是处理维度数据以确保Excel完整性
                    # 根据不同的指标类型组合处理维度归因数据
                    if has_supply_side and has_marketing_side:
                        # 同时包含供给向和营销向：维度归因使用传统GMV数据
                        traditional_results = get_attibutional_result(
                            flag, brand, tar_date_fmt, base_date_fmt, 
                            sub_brand, province, city, vender, dim_list_out, platform, upc, analysis_dimensions_list
                        )
                        sheets_data = get_sheets_data(attr_index, traditional_results, column_name_mapping, sheet_name_mapping)
                    elif has_supply_side and not has_marketing_side:
                        # 仅供给向指标：维度归因使用传统GMV数据
                        traditional_results = get_attibutional_result(
                            flag, brand, tar_date_fmt, base_date_fmt, 
                            sub_brand, province, city, vender, dim_list_out, platform, upc, analysis_dimensions_list
                        )
                        sheets_data = get_sheets_data(attr_index, traditional_results, column_name_mapping, sheet_name_mapping)
                    else:
                        # 仅营销向指标或无指标：使用原有逻辑
                        sheets_data = get_sheets_data(attr_index, results_for_detail, column_name_mapping, sheet_name_mapping)

                    # 将sheets_data中的维度数据转换为DataFrame并添加到Excel数据中
                    if sheets_data:
                        print(f"开始将维度数据添加到Excel，包含维度: {list(sheets_data.keys())}")
                        for sheet_name, sheet_data in sheets_data.items():
                            if isinstance(sheet_data, list) and sheet_data:
                                try:
                                    # 转换为DataFrame
                                    df = pd.DataFrame(sheet_data)
                                    if not df.empty:
                                        complete_data_for_excel[sheet_name] = df
                                        print(f"成功添加维度数据到Excel: {sheet_name}, 行数: {len(df)}, 列数: {len(df.columns)}")
                                    else:
                                        print(f"维度 {sheet_name} 的DataFrame为空")
                                except Exception as e:
                                    print(f"转换维度数据到DataFrame失败 {sheet_name}: {e}")
                                    continue
                            else:
                                print(f"维度 {sheet_name} 的数据格式不正确: {type(sheet_data)}")
                    else:
                        print("sheets_data为空，无法添加维度数据到Excel")
                    
                    # 处理交叉维度数据
                    if cross_dimension_enabled and cross_dimension_combinations:
                        try:
                            # 使用数据库查询获取交叉维度数据
                            if flag == 1:  # 单日期
                                # 确保日期格式正确
                                if 'tar_date_fmt' in locals() and 'base_date_fmt' in locals():
                                    cross_tar_date = tar_date_fmt
                                    cross_base_date = base_date_fmt
                                else:
                                    # 如果没有定义格式化日期，手动格式化
                                    if attr_index == '全量GMV':
                                        cross_tar_date = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
                                        cross_base_date = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
                                    else:
                                        cross_tar_date = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
                                        cross_base_date = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
                            else:  # 日期范围
                                # 确保日期格式正确
                                if 'tar_date_fmt' in locals() and 'base_date_fmt' in locals():
                                    cross_tar_date = tar_date_fmt
                                    cross_base_date = base_date_fmt
                                else:
                                    # 如果没有定义格式化日期，手动格式化
                                    if attr_index == '全量GMV':
                                        tar_start_fmt = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
                                        tar_end_fmt = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
                                        base_start_fmt = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
                                        base_end_fmt = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
                                        cross_tar_date = f"{tar_start_fmt}至{tar_end_fmt}"
                                        cross_base_date = f"{base_start_fmt}至{base_end_fmt}"
                                    else:
                                        tar_start_fmt = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
                                        tar_end_fmt = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
                                        base_start_fmt = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
                                        base_end_fmt = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
                                        cross_tar_date = f"{tar_start_fmt}至{tar_end_fmt}"
                                        cross_base_date = f"{base_start_fmt}至{base_end_fmt}"
                                
                            cross_results = get_cross_dimension_attribution_result(
                                flag=flag,
                                brand=brand,
                                tar_date=cross_tar_date,
                                base_date=cross_base_date,
                                sub_brand=sub_brand,
                                province=province,
                                city=city,
                                vender=vender,
                                cross_dimension_combinations=cross_dimension_combinations,
                                platform=platform,
                                upc=upc
                            )
                            
                            # 将交叉维度结果转换为前端需要的格式，并同时添加到Excel数据中
                            for combo_name, cross_data in cross_results.items():
                                if cross_data:
                                    headers = list(cross_data[0].keys()) if cross_data else []
                                    rows = [list(record.values()) for record in cross_data]

                                    # 为前端显示准备数据
                                    sheets_data[combo_name] = {
                                        "headers": headers,
                                        "rows": rows,
                                        "贡献度TOP10": rows[:10],
                                        "贡献度BOTTOM10": rows[-10:] if len(rows) > 10 else [],
                                        "变化率TOP10": rows[:10]
                                    }

                                    # 为Excel下载准备数据 - 将交叉维度数据转换为DataFrame
                                    try:
                                        cross_df = pd.DataFrame(cross_data)
                                        if not cross_df.empty:
                                            # 直接添加到Excel数据中，不添加筛选条件上下文信息
                                            complete_data_for_excel[combo_name] = cross_df
                                            print(f"成功添加交叉维度数据到Excel: {combo_name}, 行数: {len(cross_df)}, 列数: {len(cross_df.columns)}")
                                        else:
                                            print(f"交叉维度 {combo_name} 的DataFrame为空")
                                    except Exception as e:
                                        print(f"转换交叉维度数据到Excel失败 {combo_name}: {e}")
                                        import traceback
                                        traceback.print_exc()
                        except Exception as e:
                            print(f"处理交叉维度数据时出错: {e}")
                            import traceback
                            traceback.print_exc()
                    
                    # 添加维度归因数据到结果，确保不包含DataFrame对象
                    if isinstance(sheets_data, dict):
                        # 过滤掉任何可能的DataFrame对象
                        filtered_sheets_data = {}
                        for key, value in sheets_data.items():
                            if not isinstance(value, pd.DataFrame):
                                filtered_sheets_data[key] = value
                            else:
                                print(f"警告: 在sheets_data中发现DataFrame对象: {key}")
                        result["sheets_data"] = filtered_sheets_data
                    else:
                        result["sheets_data"] = sheets_data
                    result["market_data"] = market_data
                    result["cross_dimension_enabled"] = cross_dimension_enabled
                    result["cross_dimension_combinations"] = cross_dimension_combinations
                    result["historical_gmv_changes"] = historical_gmv_changes
                
                # 判断是否需要指标归因数据
                if "指标" in attribution_methods:
                    # 处理供给向指标
                    if has_supply_side:
                        from datadeal import get_supply_side_sheets_data
                        supply_side_data = get_supply_side_sheets_data(results_for_detail, column_name_mapping, sheet_name_mapping)

                        # 只返回可序列化的数据，排除DataFrame
                        supply_side_json_data = {k: v for k, v in supply_side_data.items() if k != 'excel_dataframe'}
                        result["supply_side_attribution"] = supply_side_json_data
                        result["supply_side_summary"] = {
                            "dimensions_analyzed": len(results_for_detail),
                            "indicators": ["GMV", "铺货门店数", "店均在售SKU数", "SKU平均动销率", "动销SKU平均GMV"],
                            "formula": "GMV = 铺货门店数 × 店均在售SKU数 × SKU平均动销率 × 动销SKU平均GMV"
                        }

                        # 将供给向指标数据添加到Excel数据中
                        if supply_side_data:
                            try:
                                # 使用新的excel_dataframe字段
                                if "excel_dataframe" in supply_side_data and not supply_side_data["excel_dataframe"].empty:
                                    complete_data_for_excel["供给向指标归因"] = supply_side_data["excel_dataframe"]
                                    print(f"成功添加供给向指标数据到Excel，数据行数: {len(supply_side_data['excel_dataframe'])}")
                                else:
                                    print("供给向指标数据的excel_dataframe为空或不存在")
                                    print(f"supply_side_data keys: {list(supply_side_data.keys())}")
                            except Exception as e:
                                print(f"处理供给向指标数据失败: {e}")
                                import traceback
                                traceback.print_exc()
                    
                    # 处理营销向指标
                    if has_marketing_side:
                        try:
                            # 获取营销向指标数据
                            marketing_data = get_marketing_side_indicators_result(
                                flag=flag,
                                brand=brand,
                                tar_date=tar_date_fmt,
                                base_date=base_date_fmt,
                                sub_brand=sub_brand,
                                province=province,
                                city=city,
                                vender=vender,
                                platform=platform,
                                upc=upc,
                                coupon_mechanism=coupon_mechanism,
                                coupon_threshold=coupon_threshold,
                                coupon_discount=coupon_discount,
                                attr_index=attr_index
                            )
                            
                            if marketing_data:
                                # 格式化营销向指标数据
                                formatted_marketing_data = get_marketing_side_sheets_data(marketing_data, attr_index)
                                # 只返回可序列化的数据，排除DataFrame
                                marketing_side_json_data = {k: v for k, v in formatted_marketing_data.items() if k not in ['excel_dataframe', 'mechanism_dataframes']}
                                result["marketing_side_attribution"] = marketing_side_json_data
                                indicators_list = ["总GMV", "活动GMV", "去重活动GMV", "活动机制核销金额", "活动机制活动ROI"] if attr_index == '活动GMV' else ["总GMV", "活动GMV", "去重活动GMV", "活动机制核销金额", "自然GMV", "活动机制活动ROI"]
                                formula_str = "GMV = 活动机制核销金额 × 活动机制活动ROI" if attr_index == '活动GMV' else "GMV = 活动机制核销金额 × 活动机制活动ROI + 自然GMV"
                                result["marketing_side_summary"] = {
                                    "indicators_count": len(marketing_data),
                                    "indicators": indicators_list,
                                    "formula": formula_str
                                }

                                # 将营销向指标数据添加到Excel数据中
                                if formatted_marketing_data:
                                    try:
                                        # 使用新的excel_dataframe字段
                                        if "excel_dataframe" in formatted_marketing_data and not formatted_marketing_data["excel_dataframe"].empty:
                                            complete_data_for_excel["营销向指标归因"] = formatted_marketing_data["excel_dataframe"]
                                            print(f"成功添加营销向指标数据到Excel，数据行数: {len(formatted_marketing_data['excel_dataframe'])}")
                                        else:
                                            print("营销向指标数据的excel_dataframe为空或不存在")
                                            print(f"formatted_marketing_data keys: {list(formatted_marketing_data.keys())}")

                                        # 添加机制详情数据到Excel
                                        if "mechanism_dataframes" in formatted_marketing_data:
                                            mechanism_dfs = formatted_marketing_data["mechanism_dataframes"]
                                            for sheet_name, mechanism_df in mechanism_dfs.items():
                                                if isinstance(mechanism_df, pd.DataFrame) and not mechanism_df.empty:
                                                    complete_data_for_excel[sheet_name] = mechanism_df
                                                    print(f"成功添加营销向机制数据到Excel: {sheet_name}，数据行数: {len(mechanism_df)}")
                                                else:
                                                    print(f"营销向机制数据为空: {sheet_name}")
                                            print(f"营销向机制数据处理完成，共添加{len(mechanism_dfs)}个机制工作表")
                                        else:
                                            print("没有找到营销向机制数据")

                                    except Exception as e:
                                        print(f"处理营销向指标数据失败: {e}")
                                        import traceback
                                        traceback.print_exc()
                            else:
                                no_data_indicators = ["总GMV", "活动GMV", "去重活动GMV", "活动机制核销金额", "活动机制活动ROI"] if attr_index == '活动GMV' else ["总GMV", "活动GMV", "去重活动GMV", "活动机制核销金额", "自然GMV", "活动机制活动ROI"]
                                result["marketing_side_summary"] = {
                                    "message": "当前筛选条件下没有营销向指标数据",
                                    "indicators": no_data_indicators
                                }
                        except Exception as e:
                            print(f"获取营销向指标数据时出错: {e}")
                            error_indicators = ["总GMV", "活动GMV", "去重活动GMV", "活动机制核销金额", "活动机制活动ROI"] if attr_index == '活动GMV' else ["总GMV", "活动GMV", "去重活动GMV", "活动机制核销金额", "自然GMV", "活动机制活动ROI"]
                            result["marketing_side_summary"] = {
                                "message": f"获取营销向指标数据失败: {str(e)}",
                                "indicators": error_indicators
                            }

                        # 获取营销向活动GMV下钻数据用于Excel导出
                        if attr_index == '活动GMV':
                            try:
                                print("开始获取营销向活动GMV下钻数据")
                                marketing_drill_down_data = get_marketing_activity_drill_down_data_for_excel(
                                    flag=flag,
                                    brand=brand,
                                    tar_date=tar_date_fmt,
                                    base_date=base_date_fmt,
                                    tar_start_date=tar_start_date_fmt,
                                    tar_end_date=tar_end_date_fmt,
                                    base_start_date=base_start_date_fmt,
                                    base_end_date=base_end_date_fmt,
                                    sub_brand=sub_brand,
                                    province=province,
                                    city=city,
                                    retailer=vender,
                                    platform=platform,
                                    upc=upc,
                                    coupon_mechanism=coupon_mechanism,
                                    coupon_threshold=coupon_threshold,
                                    coupon_discount=coupon_discount,
                                    attr_index=attr_index
                                )

                                # 将营销向活动GMV下钻数据添加到Excel数据中
                                if marketing_drill_down_data:
                                    for sheet_name, drill_df in marketing_drill_down_data.items():
                                        if isinstance(drill_df, pd.DataFrame) and not drill_df.empty:
                                            complete_data_for_excel[sheet_name] = drill_df
                                            print(f"成功添加营销向活动GMV下钻数据到Excel: {sheet_name}，数据行数: {len(drill_df)}")
                                        else:
                                            print(f"营销向活动GMV下钻数据为空: {sheet_name}")

                                    print(f"营销向活动GMV下钻数据处理完成，共添加{len(marketing_drill_down_data)}个工作表")
                                else:
                                    print("没有获取到营销向活动GMV下钻数据")

                            except Exception as e:
                                print(f"获取营销向活动GMV下钻数据失败: {e}")
                                import traceback
                                traceback.print_exc()

                # 打印Excel数据的详细信息用于调试
                print(f"准备写入Excel的数据工作表:")
                for sheet_name, data in complete_data_for_excel.items():
                    if isinstance(data, pd.DataFrame):
                        print(f"  - {sheet_name}: DataFrame, 行数={len(data)}, 列数={len(data.columns)}")
                        print(f"    列名: {list(data.columns)}")
                    else:
                        print(f"  - {sheet_name}: {type(data)}")

                # 将完整的数据（包含维度归因和指标归因）写入Excel文件
                write_dict_to_excel(complete_data_for_excel, unique_filepath)
                print(f"Excel文件已生成: {unique_filepath}")
                print(f"Excel文件包含 {len(complete_data_for_excel)} 个工作表")

                # 返回结果，确保JSON序列化安全
                safe_result = ensure_json_serializable(result)
                return jsonify(safe_result)
    except Exception as e:
        print(e)
        return jsonify({"status": "error", "message": str(e)})




@app.route('/download', methods=['GET'])
def download_file():
    """提供 Excel 文件下载 - 确保数据完整性"""
    try:
        file_path = request.args.get('file_path')
        file_name = request.args.get('file_name')

        print(f"下载请求 - 文件路径: {file_path}, 文件名: {file_name}")

        if file_path and os.path.exists(file_path):
            # 验证文件完整性
            try:
                # 尝试读取Excel文件以验证其完整性
                import pandas as pd
                excel_file = pd.ExcelFile(file_path)
                sheet_names = excel_file.sheet_names
                print(f"Excel文件验证成功，包含工作表: {sheet_names}")

                # 验证每个工作表是否包含数据
                dimension_sheets = []
                indicator_sheets = []
                cross_dimension_sheets = []
                drill_down_sheets = []

                for sheet_name in sheet_names:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    print(f"工作表 '{sheet_name}' 包含 {len(df)} 行数据，{len(df.columns)} 列")

                    # 分类工作表类型
                    if sheet_name in ['子品牌', '省份', '城市', '零售商', '商品', '券机制', '券门槛', '优惠力度', '平台']:
                        dimension_sheets.append(sheet_name)
                        # 维度归因数据应包含基本的GMV相关列
                        expected_cols = ['当期GMV', '基准GMV', 'GMV变化值', 'GMV变化率', 'GMV贡献度'] if '活动' not in str(df.columns) else ['当期活动GMV', '对比期活动GMV', '活动GMV变化值', '活动GMV变化率', '活动GMV贡献度']
                        existing_cols = [col for col in expected_cols if col in df.columns]
                        print(f"维度工作表 '{sheet_name}' 包含GMV相关列: {existing_cols}")
                    elif sheet_name in ['供给向指标归因', '营销向指标归因']:
                        indicator_sheets.append(sheet_name)
                        # 指标归因数据应包含指标相关列
                        required_cols = ['指标名称', '当期值', '对比期值', '变化值', '变化率', '贡献度']
                        existing_cols = [col for col in required_cols if col in df.columns]
                        print(f"指标工作表 '{sheet_name}' 包含指标相关列: {existing_cols}")
                    elif sheet_name.startswith('营销向活动GMV机制'):
                        # 营销向活动GMV机制数据工作表
                        indicator_sheets.append(sheet_name)
                        # 机制数据应包含机制相关列
                        if '机制详情' in sheet_name:
                            expected_cols = ['机制名称', '机制类型', '当期GMV', '对比期GMV', 'GMV变化值', 'GMV变化率', 'GMV贡献度']
                        else:  # 机制类型
                            expected_cols = ['机制类型', '当期GMV', '对比期GMV', 'GMV变化值', 'GMV变化率', 'GMV贡献度']
                        existing_cols = [col for col in expected_cols if col in df.columns]
                        print(f"机制工作表 '{sheet_name}' 包含机制相关列: {existing_cols}")

                        # 验证机制数据完整性
                        if len(df) > 0:
                            print(f"机制工作表 '{sheet_name}' 包含 {len(df)} 行机制数据")
                        else:
                            print(f"机制工作表 '{sheet_name}' 数据为空")
                    elif sheet_name.startswith('活动GMV下钻-'):
                        # 营销向活动GMV下钻工作表
                        drill_down_sheets.append(sheet_name)
                        # 下钻数据应包含下钻相关列
                        expected_cols = ['当期GMV', '对比期GMV', 'GMV变化值', 'GMV变化率', 'GMV贡献度']
                        existing_cols = [col for col in expected_cols if col in df.columns]
                        print(f"下钻工作表 '{sheet_name}' 包含下钻相关列: {existing_cols}")

                        # 验证下钻数据完整性
                        if len(df) > 0:
                            print(f"下钻工作表 '{sheet_name}' 包含 {len(df)} 行下钻数据")
                        else:
                            print(f"下钻工作表 '{sheet_name}' 数据为空")
                    elif '-' in sheet_name:
                        # 交叉维度工作表（包含连字符的工作表名）
                        cross_dimension_sheets.append(sheet_name)
                        # 交叉维度数据应包含交叉维度相关列
                        expected_cols = ['交叉维度名称', '当期GMV', '对比期GMV', 'GMV变化值', 'GMV变化率', 'GMV贡献度']
                        existing_cols = [col for col in expected_cols if col in df.columns]
                        print(f"交叉维度工作表 '{sheet_name}' 包含交叉维度相关列: {existing_cols}")

                        # 验证数据完整性（不再检查筛选条件信息）
                        if len(df) > 0:
                            print(f"交叉维度工作表 '{sheet_name}' 包含 {len(df)} 行交叉维度数据")
                        else:
                            print(f"交叉维度工作表 '{sheet_name}' 数据为空")
                    else:
                        print(f"其他类型工作表: '{sheet_name}'")

                print(f"数据完整性验证完成:")
                print(f"  - 维度归因工作表: {dimension_sheets}")
                print(f"  - 指标归因工作表: {indicator_sheets}")
                print(f"  - 交叉维度工作表: {cross_dimension_sheets}")
                print(f"  - 营销向活动GMV下钻工作表: {drill_down_sheets}")
                print(f"  - 总工作表数: {len(sheet_names)}")

                excel_file.close()

            except Exception as e:
                print(f"Excel文件验证失败: {e}")
                return f"Excel文件损坏或格式错误: {str(e)}", 500

            # 如果提供了自定义文件名，则使用它
            if file_name:
                return send_file(file_path, as_attachment=True, download_name=file_name)
            else:
                # 否则使用原始文件名
                return send_file(file_path, as_attachment=True)

        # 如果没有指定文件路径，则尝试下载默认文件
        if os.path.exists(OUTPUT_FILE_PATH):
            print(f"使用默认文件: {OUTPUT_FILE_PATH}")
            return send_file(OUTPUT_FILE_PATH, as_attachment=True)

        return "文件未找到，请先运行归因分析", 404

    except Exception as e:
        print(f"下载文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return f"下载失败: {str(e)}", 500


def generate_cross_dimension_data(combo, file_path, attr_index):
    """
    生成交叉维度的数据
    
    Args:
        combo: 交叉维度组合信息
        file_path: Excel文件路径
        attr_index: 归因指标类型
    
    Returns:
        dict: 交叉维度数据
    """
    try:
        import pandas as pd
        import itertools
        
        print(f"开始生成交叉维度数据: {combo}")
        print(f"Excel文件路径: {file_path}")
        
        # 读取各个维度的数据
        dimension_data = {}
        for chinese_dim in combo['chinese_dimensions']:
            try:
                print(f"尝试读取维度 {chinese_dim} 的数据...")
                df = pd.read_excel(file_path, sheet_name=chinese_dim)
                print(f"维度 {chinese_dim} 数据行数: {len(df)}")
                print(f"维度 {chinese_dim} 列名: {list(df.columns)}")
                
                if len(df) > 0:
                    print(f"维度 {chinese_dim} 前3行数据:")
                    print(df.head(3))
                    dimension_data[chinese_dim] = df
                else:
                    print(f"维度 {chinese_dim} 数据为空")
            except Exception as e:
                print(f"读取维度 {chinese_dim} 数据失败: {e}")
                continue
        
        if not dimension_data:
            print("所有维度数据读取失败")
            return None
        
        print(f"成功读取的维度: {list(dimension_data.keys())}")
        
        # 生成交叉维度的组合数据
        cross_data = []
        
        # 准备每个维度的数据（限制数量避免组合爆炸）
        dimension_items = {}
        for dim in combo['chinese_dimensions']:
            if dim not in dimension_data:
                continue
                
            df = dimension_data[dim]
            items = []
            
            # 根据维度数量动态调整每个维度的条目数
            max_items = max(3, min(8, 50 // len(combo['chinese_dimensions'])))
            
            for idx, (_, row) in enumerate(df.head(max_items).iterrows()):
                # 获取维度名称
                name = "未知"
                possible_name_fields = [dim, '名称', f'{dim}名称', df.columns[0]]
                for field in possible_name_fields:
                    if field in row.index and pd.notna(row[field]):
                        name = str(row[field])
                        break
                
                # 获取GMV值
                gmv_value = 0
                gmv_fields = ['GMV当前值', 'gmv_target', 'GMV基准值', 'gmv_base', '当前值', '基准值']
                for field in gmv_fields:
                    if field in row.index and pd.notna(row[field]):
                        try:
                            gmv_value = float(row[field])
                            break
                        except:
                            continue
                items.append({
                    'name': name,
                    'gmv': gmv_value,
                    'row_data': row
                })
            
            dimension_items[dim] = items
            print(f"维度 {dim} 准备了 {len(items)} 个条目")
        
        # 生成所有维度的笛卡尔积
        dimensions = list(dimension_items.keys())
        if len(dimensions) < 2:
            print("至少需要2个维度才能进行交叉")
            return None
        
        # 获取每个维度的条目列表
        item_lists = [dimension_items[dim] for dim in dimensions]
        
        print(f"准备生成 {len(dimensions)} 个维度的交叉组合")
        
        # 生成笛卡尔积
        for combination in itertools.product(*item_lists):
            # 创建交叉维度记录
            cross_record = {}
            names = []
            total_gmv = 0
            
            # 添加各个维度的信息
            for i, (dim, item) in enumerate(zip(dimensions, combination)):
                names.append(item['name'])
                total_gmv += item['gmv']
            
            # 创建交叉维度名称
            cross_name = '-'.join(names)
            cross_record['交叉维度名称'] = cross_name
            
            # 计算GMV相关指标
            cross_gmv = total_gmv / len(combination)  # 平均值作为交叉GMV
            base_gmv = cross_gmv * 0.9  # 简化处理
            gmv_change = cross_gmv - base_gmv
            gmv_change_rate = (gmv_change / base_gmv * 100) if base_gmv > 0 else 10.0
            
            # 计算贡献度（简化处理）
            total_market_gmv = sum(sum(item['gmv'] for item in items) for items in dimension_items.values())
            contribution = (cross_gmv / total_market_gmv * 100) if total_market_gmv > 0 else 5.0
            
            # 计算占比
            target_ratio = 100  # 简化处理，设置默认占比
            
            # 根据attr_index使用正确的字段名
            gmv_field = '当期GMV' if attr_index == '全量GMV' else '当期活动GMV'
            
            # 只添加必要的GMV相关指标，使用统一的字段名称
            cross_record.update({
                gmv_field: round(cross_gmv, 2),
                '占比': f"{target_ratio:.2f}%",
                '对比GMV': round(base_gmv, 2),
                'GMV变化值': round(gmv_change, 2),
                'GMV变化率': f"{gmv_change_rate:+.2f}%",
                'GMV贡献度': f"{contribution:+.2f}%"
            })
            
            cross_data.append(cross_record)
        
        print(f"生成的交叉数据条数: {len(cross_data)}")
        
        # 按GMV字段排序
        gmv_field = '当期GMV' if attr_index == '全量GMV' else '当期活动GMV'
        cross_data.sort(key=lambda x: x[gmv_field], reverse=True)
        
        # 限制返回数据量
        cross_data = cross_data[:100]  # 增加限制数量
        
        if cross_data:
            headers = list(cross_data[0].keys())
            rows = [list(record.values()) for record in cross_data]
            
            print(f"返回的数据结构 - headers: {len(headers)}个, rows: {len(rows)}行")
            print(f"Headers: {headers}")
            
            return {
                "headers": headers,
                "rows": rows,
                "贡献度TOP10": rows[:10],
                "贡献度BOTTOM10": rows[-10:],
                "变化率TOP10": rows[:10]
            }
        else:
            print("没有生成任何交叉数据")
        
        return None
        
    except Exception as e:
        print(f"生成交叉维度数据失败: {e}")
        import traceback
        traceback.print_exc()
        return None

@app.route('/drill_down', methods=['POST'])
def drill_down():
    """处理维度下钻逻辑"""
    try:
        # 获取基础参数
        brand = request.form.get('brand')
        attr_index = request.form.get('attr_index')
        target_dimension = request.form.get('target_dimension')  # 目标下钻维度
        
        # 获取日期参数
        tar_date = request.form.get('tar_date')
        base_date = request.form.get('base_date')
        tar_start_date = request.form.get('tar_start_date')
        tar_end_date = request.form.get('tar_end_date')
        base_start_date = request.form.get('base_start_date')
        base_end_date = request.form.get('base_end_date')
        
        # 确定flag值：1表示单日期，2表示日期范围
        if tar_date and base_date:
            flag = 1
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            flag = 2
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400
        
        # 获取多层下钻筛选条件 - 直接从请求参数中获取
        # 这些参数已经包含了完整的下钻路径筛选条件
        sub_brand = request.form.get('sub_brand', '全部')
        province = request.form.get('province', '全部')  
        city = request.form.get('standard_city', '全部')  # 注意：城市字段使用standard_city
        retailer = request.form.get('retailer', '全部')
        platform = request.form.get('platform', '全部')
        upc = request.form.get('upc', '全部')  # 优先使用upc字段
        product_name = request.form.get('search_text', '全部')  # 商品维度
        
        # 券相关筛选条件（活动GMV时使用）
        coupon_mechanism = request.form.get('coupon_mechanism', '全部')
        coupon_threshold = request.form.get('coupon_threshold', '全部')
        coupon_discount = request.form.get('coupon_discount', '全部')
        
        # 记录接收到的所有下钻筛选条件
        drill_filters = {
            'sub_brand': sub_brand,
            'province': province,
            'city': city,
            'retailer': retailer,
            'platform': platform,
            'upc': upc,
            'product_name': product_name,
            'coupon_mechanism': coupon_mechanism,
            'coupon_threshold': coupon_threshold,
            'coupon_discount': coupon_discount
        }
        
        print(f"下钻查询参数: brand={brand}, attr_index={attr_index}, target_dimension={target_dimension}")
        print(f"完整下钻筛选条件: {drill_filters}")
        print(f"多层下钻SQL构建 - 平台:{platform}, 省份:{province}, 城市:{city}, 零售商:{retailer}, 子品牌:{sub_brand}, 商品:{product_name}")
        
        # 根据归因指标类型调用不同的处理函数
        if attr_index == '全量GMV':
            result = get_drill_down_gmv_result(
                flag=flag,
                brand=brand,
                tar_date=tar_date,
                base_date=base_date,
                tar_start_date=tar_start_date,
                tar_end_date=tar_end_date,
                base_start_date=base_start_date,
                base_end_date=base_end_date,
                target_dimension=target_dimension,
                sub_brand=sub_brand,
                province=province,
                city=city,
                retailer=retailer,
                platform=platform,
                upc=upc,
                product_name=product_name
            )
        elif attr_index == '活动GMV':
            result = get_drill_down_activity_result(
                flag=flag,
                brand=brand,
                tar_date=tar_date,
                base_date=base_date,
                tar_start_date=tar_start_date,
                tar_end_date=tar_end_date,
                base_start_date=base_start_date,
                base_end_date=base_end_date,
                target_dimension=target_dimension,
                sub_brand=sub_brand,
                province=province,
                city=city,
                retailer=retailer,
                platform=platform,
                upc=upc,
                coupon_mechanism=coupon_mechanism,
                coupon_threshold=coupon_threshold,
                coupon_discount=coupon_discount
            )
        else:
            return jsonify({"status": "error", "message": "不支持的归因指标类型"}), 400
        
        if result:
            # 格式化返回数据
            sheets_data = {}
            
            # 转换维度名称映射
            dimension_name_mapping = {
                '平台': '平台',
                '省份': '省份', 
                '城市': '城市',
                '零售商': '零售商',
                '子品牌': '子品牌',
                '商品名称': '商品',
                '券机制': '券机制',
                '券门槛': '券门槛',
                '优惠力度': '优惠力度'
            }
            
            sheet_name = dimension_name_mapping.get(target_dimension, target_dimension)
            
            # 转换数据格式 - 直接使用对象数组格式，与主表格保持一致
            if result and len(result) > 0:
                sheets_data[sheet_name] = result
            
            return jsonify({
                "status": "success",
                "message": f"成功获取{target_dimension}维度下钻数据",
                "sheets_data": sheets_data,
                "target_dimension": sheet_name
            })
        else:
            return jsonify({
                "status": "error", 
                "message": "未找到下钻数据"
            }), 404
            
    except Exception as e:
        print(f"下钻查询出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": f"下钻查询失败: {str(e)}"}), 500

@app.route('/get_metrics_decomposition', methods=['GET'])
def get_metrics_decomposition():
    """
    获取指标拆解数据
    根据用户选择的维度值和筛选条件，返回对应的营销向和供给向指标数据
    """
    try:
        # 获取参数
        params = process_multi_select_params(request.args)
        
        # 基础参数
        brand = params['brand']
        sub_brand = params['sub_brand']
        province = params['province']
        city = params['city']
        vender = params['retailer']
        platform = params['platform']
        upc = params['upc']
        
        # 活动GMV相关参数
        coupon_mechanism = params['coupon_mechanism']
        coupon_threshold = params['coupon_threshold']
        coupon_discount = params['coupon_discount']
        
        # 获取选中的维度和值
        selected_dimension = request.args.get('selected_dimension')
        selected_value = request.args.get('selected_value')

        # 获取目标维度参数（参考drill_down接口）
        target_dimension = request.args.get('target_dimension')

        # 获取归因设置
        attribution_methods = params['attribution_methods']
        indicator_types = params['indicator_types']
        attr_index = params.get('attr_index', '全量GMV')  # 获取attr_index参数

        # 获取分析维度参数（参考get_result_data接口的格式）
        analysis_dimensions = params.get('analysis_dimensions', '')

        # 将前端传入的中文维度转换为英文维度列表（参考get_result_data接口）
        analysis_dimensions_list = convert_analysis_dimensions(analysis_dimensions, attr_index)

        print("\n============= 指标拆解接口参数 =============")
        print(f"brand: {brand}")
        print(f"selected_dimension: {selected_dimension}")
        print(f"selected_value: {selected_value}")
        print(f"target_dimension: {target_dimension}")
        print(f"platform: {platform}")
        print(f"attr_index: {attr_index}")
        print(f"indicator_types: {indicator_types}")
        print(f"analysis_dimensions: {analysis_dimensions}")
        print(f"analysis_dimensions_list: {analysis_dimensions_list}")
        print(f"tar_start_date: {params['tar_start_date']}")
        print(f"tar_end_date: {params['tar_end_date']}")
        print(f"base_start_date: {params['base_start_date']}")
        print(f"base_end_date: {params['base_end_date']}")
        print("=========================================\n")

        # 日期参数
        tar_date = params['tar_date']
        base_date = params['base_date']
        tar_start_date = params['tar_start_date']
        tar_end_date = params['tar_end_date']
        base_start_date = params['base_start_date']
        base_end_date = params['base_end_date']
        
        # 确定flag值
        if tar_date and base_date:
            flag = 1
            tar_date_fmt = f"{tar_date[:4]}-{tar_date[4:6]}-{tar_date[6:]}"
            base_date_fmt = f"{base_date[:4]}-{base_date[4:6]}-{base_date[6:]}"
            if tar_date_fmt == base_date_fmt:
                return jsonify({
                    "status": "time_fail",
                    "message": "目标日期和对比日期不能相同，请重新选择！"
                })
        elif tar_start_date and tar_end_date and base_start_date and base_end_date:
            flag = 2
            tar_start_date_fmt = f"{tar_start_date[:4]}-{tar_start_date[4:6]}-{tar_start_date[6:]}"
            tar_end_date_fmt = f"{tar_end_date[:4]}-{tar_end_date[4:6]}-{tar_end_date[6:]}"
            base_start_date_fmt = f"{base_start_date[:4]}-{base_start_date[4:6]}-{base_start_date[6:]}"
            base_end_date_fmt = f"{base_end_date[:4]}-{base_end_date[4:6]}-{base_end_date[6:]}"
            if tar_start_date_fmt == base_start_date_fmt and tar_end_date_fmt == base_end_date_fmt:
                return jsonify({
                    "status": "time_fail",
                    "message": "目标日期范围和对比日期范围不能相同，请重新选择！"
                })
            tar_date_fmt = f"{tar_start_date_fmt}至{tar_end_date_fmt}"
            base_date_fmt = f"{base_start_date_fmt}至{base_end_date_fmt}"
        else:
            return jsonify({"status": "error", "message": "日期参数不完整"}), 400
        
        # 根据选中的维度和值，更新筛选条件
        if selected_dimension and selected_value:
            # 维度映射
            dimension_mapping = {
                '平台': 'platform',
                '省份': 'province',
                '城市': 'city',
                '零售商': 'retailer',
                '子品牌': 'sub_brand',
                '商品': 'upc',
                '券机制': 'coupon_mechanism',
                '券门槛': 'coupon_threshold',
                '优惠力度': 'coupon_discount'
            }
            
            # 更新对应的筛选条件
            if selected_dimension in dimension_mapping:
                param_key = dimension_mapping[selected_dimension]
                if param_key == 'retailer':
                    vender = selected_value
                elif param_key == 'city':
                    city = selected_value
                elif param_key == 'sub_brand':
                    sub_brand = selected_value
                elif param_key == 'province':
                    province = selected_value
                elif param_key == 'platform':
                    platform = selected_value
                elif param_key == 'upc':
                    upc = selected_value
                elif param_key == 'coupon_mechanism':
                    coupon_mechanism = selected_value
                elif param_key == 'coupon_threshold':
                    coupon_threshold = selected_value
                elif param_key == 'coupon_discount':
                    coupon_discount = selected_value
        
        result = {
            "status": "success",
            "selected_dimension": selected_dimension,
            "selected_value": selected_value
        }
        
        # 检查是否包含供给向指标
        has_supply_side = has_supply_side_indicator(indicator_types)
        has_marketing_side = has_marketing_side_indicator(indicator_types)
        
        print("\n============= 指标类型检查 =============")
        print(f"has_supply_side: {has_supply_side}")
        print(f"has_marketing_side: {has_marketing_side}")
        print("=====================================\n")
        
        # 获取营销向指标数据
        if has_marketing_side:
            try:
                print("\n============= 开始获取营销向指标数据 =============")
                print("调用 get_marketing_side_indicators_result 函数，参数如下：")
                print(f"flag={flag}")
                print(f"brand={brand}")
                print(f"tar_date={tar_date_fmt}")
                print(f"base_date={base_date_fmt}")
                print(f"sub_brand={sub_brand}")
                print(f"province={province}")
                print(f"city={city}")
                print(f"vender={vender}")
                print(f"platform={platform}")
                print(f"upc={upc}")
                print(f"coupon_mechanism={coupon_mechanism}")
                print(f"coupon_threshold={coupon_threshold}")
                print(f"coupon_discount={coupon_discount}")
                print(f"attr_index={attr_index}")
                print("==============================================\n")
                
                marketing_data = get_marketing_side_indicators_result(
                    flag=flag,
                    brand=brand,
                    tar_date=tar_date_fmt,
                    base_date=base_date_fmt,
                    sub_brand=sub_brand,
                    province=province,
                    city=city,
                    vender=vender,
                    platform=platform,
                    upc=upc,
                    coupon_mechanism=coupon_mechanism,
                    coupon_threshold=coupon_threshold,
                    coupon_discount=coupon_discount,
                    attr_index=attr_index
                )
                
                print("\n============= 营销向指标数据结果 =============")
                print(f"marketing_data 是否为空: {marketing_data is None}")
                if marketing_data:
                    print(f"marketing_data 长度: {len(marketing_data)}")
                    print("marketing_data 前几行:")
                    print(marketing_data[:3] if isinstance(marketing_data, list) else marketing_data)
                print("===========================================\n")
                
                if marketing_data:
                    formatted_marketing_data = get_marketing_side_sheets_data(marketing_data, attr_index)
                    # 只返回可序列化的数据，排除DataFrame
                    marketing_side_json_data = {k: v for k, v in formatted_marketing_data.items() if k not in ['excel_dataframe', 'mechanism_dataframes']}
                    result["marketing_side_data"] = marketing_side_json_data
                else:
                    result["marketing_side_data"] = {
                        "message": "当前筛选条件下没有营销向指标数据"
                    }
            except Exception as e:
                print(f"\n============= 营销向指标数据获取出错 =============")
                print(f"错误信息: {str(e)}")
                print("==============================================\n")
                result["marketing_side_data"] = {
                    "message": f"获取营销向指标数据失败: {str(e)}"
                }
        
        # 获取供给向指标数据
        if has_supply_side:
            try:
                # 指标拆解接口是直接访问指标归因的场景
                supply_results = get_supply_side_attribution_result(
                    flag, brand, tar_date_fmt, base_date_fmt,
                    sub_brand, province, city, vender,
                    analysis_dimensions_list, platform, upc,
                    from_dimension_attribution=True
                )
                
                if supply_results:
                    supply_side_data = get_supply_side_sheets_data(
                        supply_results, column_name_mapping, sheet_name_mapping
                    )
                    # 只返回可序列化的数据，排除DataFrame
                    supply_side_json_data = {k: v for k, v in supply_side_data.items() if k != 'excel_dataframe'}
                    result["supply_side_data"] = supply_side_json_data
                else:
                    result["supply_side_data"] = {
                        "message": "当前筛选条件下没有供给向指标数据"
                    }
            except Exception as e:
                print(f"获取供给向指标数据时出错: {e}")
                result["supply_side_data"] = {
                    "message": f"获取供给向指标数据失败: {str(e)}"
                }
        
        return jsonify(result)
        
    except Exception as e:
        print(f"获取指标拆解数据时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == "__main__":
    app.run(debug=True, host='0.0.0.0', port=6003, threaded=True)